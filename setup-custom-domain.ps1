# PowerShell script to set up custom domain for CodeSpace
# Run as Administrator

Write-Host "Setting up custom domain 'codespace.roy' for CodeSpace development..." -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run as Administrator." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Hosts file path
$hostsPath = "C:\Windows\System32\drivers\etc\hosts"

# Domain entry to add
$domainEntry = "127.0.0.1    codespace.roy"

# Check if entry already exists
$hostsContent = Get-Content $hostsPath
$entryExists = $hostsContent | Where-Object { $_ -match "codespace\.roy" }

if ($entryExists) {
    Write-Host "Domain 'codespace.roy' already exists in hosts file." -ForegroundColor Yellow
} else {
    # Add the domain entry
    Add-Content -Path $hostsPath -Value $domainEntry
    Write-Host "Added 'codespace.roy' to hosts file." -ForegroundColor Green
}

Write-Host "`nCustom domain setup complete!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Start the API server: cd CSAPI && npm run dev" -ForegroundColor White
Write-Host "2. Start the UI server: cd CSUI && npm run dev" -ForegroundColor White
Write-Host "3. Access your app at: http://codespace.roy:5173" -ForegroundColor White
Write-Host "`nNote: Make sure both servers are running on the correct ports:" -ForegroundColor Yellow
Write-Host "- UI: http://codespace.roy:5173" -ForegroundColor White
Write-Host "- API: http://codespace.roy:3000" -ForegroundColor White

Read-Host "`nPress Enter to exit"
