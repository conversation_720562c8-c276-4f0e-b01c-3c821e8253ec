import React, { useState } from 'react';

const EditorSidebar = ({ 
  onRunCode, 
  onSaveFile, 
  onShare, 
  shareUrl, 
  showShareTooltip,
  activeFile,
  onLanguageChange,
  isPublic
}) => {
  const [wordWrap, setWordWrap] = useState(false);
  const [fontSize, setFontSize] = useState('14px');
  const [theme, setTheme] = useState('Dark');

  // Handle language change
  const handleLanguageChange = (e) => {
    if (onLanguageChange) {
      onLanguageChange(e.target.value);
    }
  };

  // Handle font size change
  const handleFontSizeChange = (e) => {
    setFontSize(e.target.value);
    // Apply font size to editor
    const editor = document.querySelector('.code-editor textarea');
    if (editor) {
      editor.style.fontSize = e.target.value;
    }
  };

  // Handle word wrap toggle
  const handleWordWrapToggle = () => {
    setWordWrap(!wordWrap);
    // Apply word wrap to editor
    const editor = document.querySelector('.code-editor textarea');
    if (editor) {
      editor.style.whiteSpace = !wordWrap ? 'pre-wrap' : 'pre';
    }
  };

  return (
    <aside className="w-64 bg-gray-800/80 backdrop-blur-sm border-l border-gray-700/50 text-white p-4 relative z-10">
      <h3 className="text-lg font-medium mb-4">Tools</h3>
      
      {/* Action buttons */}
      <div className="space-y-3">
        <button 
          onClick={onRunCode}
          className="w-full flex items-center justify-between py-2 px-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors"
        >
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Run Code
          </span>
          <span className="text-xs text-gray-400">Ctrl+Enter</span>
        </button>
        
        <button 
          id="save-button"
          onClick={onSaveFile}
          className="w-full flex items-center justify-between py-2 px-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors"
        >
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
            </svg>
            Save
          </span>
          <span className="text-xs text-gray-400">Ctrl+S</span>
        </button>
        
        <div className="relative">
          <button 
            onClick={onShare}
            className={`w-full flex items-center justify-between py-2 px-3 rounded-lg transition-colors ${
              isPublic ? 'bg-green-700/50 hover:bg-green-700' : 'bg-gray-700/50 hover:bg-gray-700'
            }`}
          >
            <span className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
              </svg>
              {isPublic ? 'Copy Share Link' : 'Make Public & Share'}
            </span>
          </button>
          
          {/* Share tooltip */}
          {showShareTooltip && (
            <div className="absolute top-full left-0 right-0 mt-2 p-2 bg-gray-700 text-white text-xs rounded text-center">
              Link copied to clipboard!
            </div>
          )}
          
          {/* Share URL display */}
          {shareUrl && (
            <div className="mt-2 p-2 bg-gray-700/50 rounded text-xs text-gray-300 break-all">
              {shareUrl}
            </div>
          )}
        </div>
      </div>
      
      {/* Language selector */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-400 mb-2">Language</label>
        <select 
          className="w-full bg-gray-700 border border-gray-600 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-green-500"
          value={activeFile?.language || 'javascript'}
          onChange={handleLanguageChange}
        >
          <option value="javascript">JavaScript</option>
          <option value="python">Python</option>
          <option value="java">Java</option>
          <option value="cpp">C++</option>
          <option value="html">HTML</option>
          <option value="css">CSS</option>
        </select>
      </div>
      
      {/* Settings section */}
      <div className="mt-6">
        <h4 className="text-sm font-medium text-gray-400 mb-2">Editor Settings</h4>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Theme</span>
            <select 
              className="bg-gray-700 border border-gray-600 rounded py-1 px-2 text-sm text-white focus:outline-none focus:ring-1 focus:ring-green-500"
              value={theme}
              onChange={(e) => setTheme(e.target.value)}
            >
              <option>Dark</option>
              <option>Light</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">Font Size</span>
            <select 
              className="bg-gray-700 border border-gray-600 rounded py-1 px-2 text-sm text-white focus:outline-none focus:ring-1 focus:ring-green-500"
              value={fontSize}
              onChange={handleFontSizeChange}
            >
              <option>12px</option>
              <option>14px</option>
              <option>16px</option>
              <option>18px</option>
            </select>
          </div>
          <div className="flex items-center">
            <input 
              type="checkbox" 
              id="wrap" 
              className="mr-2" 
              checked={wordWrap}
              onChange={handleWordWrapToggle}
            />
            <label htmlFor="wrap" className="text-sm">Word Wrap</label>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default EditorSidebar;
