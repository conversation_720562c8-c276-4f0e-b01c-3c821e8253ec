import React, { useState, useRef, useEffect } from 'react';

const Terminal = ({ isOpen, onClose, initialOutput = '' }) => {
  const [output, setOutput] = useState(initialOutput);
  const [isMinimized, setIsMinimized] = useState(false);
  const terminalRef = useRef(null);

  // Auto-scroll to bottom when output changes
  useEffect(() => {
    if (terminalRef.current && !isMinimized) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [output, isMinimized]);

  // Handle terminal close
  const handleClose = () => {
    setOutput('');
    onClose();
  };

  // Toggle minimize/maximize
  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  // Clear terminal output
  const clearTerminal = () => {
    setOutput('');
  };

  if (!isOpen) return null;

  return (
    <div
      className={`fixed bottom-0 right-0 w-full md:w-1/2 lg:w-2/5 transition-all duration-300 ease-in-out z-tooltip ${
        isMinimized ? 'h-10' : 'h-64'
      }`}
      style={{ maxWidth: '600px' }}
    >
      <div className="terminal h-full flex flex-col shadow-lg border border-gray-700">
        <div className="terminal-header">
          <div className="flex items-center">
            <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
            <div className="h-3 w-3 rounded-full bg-yellow-500 mr-2"></div>
            <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
            <span className="text-sm font-medium text-gray-300">Terminal</span>
          </div>
          <div className="flex items-center">
            <button
              onClick={clearTerminal}
              className="text-gray-400 hover:text-gray-200 mr-3 text-xs"
              title="Clear terminal"
            >
              Clear
            </button>
            <button
              onClick={toggleMinimize}
              className="text-gray-400 hover:text-gray-200 mr-3"
              title={isMinimized ? "Maximize" : "Minimize"}
            >
              {isMinimized ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              )}
            </button>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-red-400"
              title="Close terminal"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {!isMinimized && (
          <div
            ref={terminalRef}
            className="terminal-body flex-grow overflow-y-auto"
          >
            {output ? (
              <pre className="text-sm">{output}</pre>
            ) : (
              <div className="text-gray-500 text-sm">$ Ready</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Terminal;
