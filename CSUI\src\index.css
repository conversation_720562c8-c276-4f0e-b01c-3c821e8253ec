@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.7);
  border-radius: 4px;
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8);
}

/* Hide scrollbar when not in use but keep functionality */
.hide-scrollbar::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Terminal styles */
.terminal {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background-color: #1a1a1a;
  color: #f0f0f0;
  border-radius: 6px;
  overflow: auto;
  transition: all 0.3s ease;
}

.terminal-header {
  background-color: #2d2d2d;
  padding: 6px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #3a3a3a;
}

.terminal-body {
  padding: 12px;
  white-space: pre-wrap;
}

.terminal-body .success {
  color: #4ade80;
}

.terminal-body .error {
  color: #f87171;
}

.terminal-body .info {
  color: #60a5fa;
}

/* Z-index layers */
.z-dropdown {
  z-index: 50;
}

.z-modal {
  z-index: 100;
}

.z-tooltip {
  z-index: 60;
}
