{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/markdown/markdown.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/markdown/markdown.ts\nvar conf = {\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".md\",\n  // escape codes\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  noncontrol: /[^\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  // escape codes for javascript/CSS strings\n  jsescapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n  // non matched elements\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  tokenizer: {\n    root: [\n      // markdown tables\n      [/^\\s*\\|/, \"@rematch\", \"@table_header\"],\n      // headers (with #)\n      [/^(\\s{0,3})(#+)((?:[^\\\\#]|@escapes)+)((?:#+)?)/, [\"white\", \"keyword\", \"keyword\", \"keyword\"]],\n      // headers (with =)\n      [/^\\s*(=+|\\-+)\\s*$/, \"keyword\"],\n      // headers (with ***)\n      [/^\\s*((\\*[ ]?)+)\\s*$/, \"meta.separator\"],\n      // quote\n      [/^\\s*>+/, \"comment\"],\n      // list (starting with * or number)\n      [/^\\s*([\\*\\-+:]|\\d+\\.)\\s/, \"keyword\"],\n      // code block (4 spaces indent)\n      [/^(\\t|[ ]{4})[^ ].*$/, \"string\"],\n      // code block (3 tilde)\n      [/^\\s*~~~\\s*((?:\\w|[\\/\\-#])+)?\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // github style code blocks (with backticks and language)\n      [\n        /^\\s*```\\s*((?:\\w|[\\/\\-#])+).*$/,\n        { token: \"string\", next: \"@codeblockgh\", nextEmbedded: \"$1\" }\n      ],\n      // github style code blocks (with backticks but no language)\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // markup within lines\n      { include: \"@linecontent\" }\n    ],\n    table_header: [\n      { include: \"@table_common\" },\n      [/[^\\|]+/, \"keyword.table.header\"]\n      // table header\n    ],\n    table_body: [{ include: \"@table_common\" }, { include: \"@linecontent\" }],\n    table_common: [\n      [/\\s*[\\-:]+\\s*/, { token: \"keyword\", switchTo: \"table_body\" }],\n      // header-divider\n      [/^\\s*\\|/, \"keyword.table.left\"],\n      // opening |\n      [/^\\s*[^\\|]/, \"@rematch\", \"@pop\"],\n      // exiting\n      [/^\\s*$/, \"@rematch\", \"@pop\"],\n      // exiting\n      [\n        /\\|/,\n        {\n          cases: {\n            \"@eos\": \"keyword.table.right\",\n            // closing |\n            \"@default\": \"keyword.table.middle\"\n            // inner |\n          }\n        }\n      ]\n    ],\n    codeblock: [\n      [/^\\s*~~~\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    // github style code blocks\n    codeblockgh: [\n      [/```\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^`]+/, \"variable.source\"]\n    ],\n    linecontent: [\n      // escapes\n      [/&\\w+;/, \"string.escape\"],\n      [/@escapes/, \"escape\"],\n      // various markup\n      [/\\b__([^\\\\_]|@escapes|_(?!_))+__\\b/, \"strong\"],\n      [/\\*\\*([^\\\\*]|@escapes|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\b_[^_]+_\\b/, \"emphasis\"],\n      [/\\*([^\\\\*]|@escapes)+\\*/, \"emphasis\"],\n      [/`([^\\\\`]|@escapes)+`/, \"variable\"],\n      // links\n      [/\\{+[^}]+\\}+/, \"string.target\"],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\]\\([^\\)]+\\))/, [\"string.link\", \"\", \"string.link\"]],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\])/, \"string.link\"],\n      // or html\n      { include: \"html\" }\n    ],\n    // Note: it is tempting to rather switch to the real HTML mode instead of building our own here\n    // but currently there is a limitation in Monarch that prevents us from doing it: The opening\n    // '<' would start the HTML mode, however there is no way to jump 1 character back to let the\n    // HTML mode also tokenize the opening angle bracket. Thus, even though we could jump to HTML,\n    // we cannot correctly tokenize it in that mode yet.\n    html: [\n      // html tags\n      [/<(\\w+)\\/>/, \"tag\"],\n      [\n        /<(\\w+)(\\-|\\w)*/,\n        {\n          cases: {\n            \"@empty\": { token: \"tag\", next: \"@tag.$1\" },\n            \"@default\": { token: \"tag\", next: \"@tag.$1\" }\n          }\n        }\n      ],\n      [/<\\/(\\w+)(\\-|\\w)*\\s*>/, { token: \"tag\" }],\n      [/<!--/, \"comment\", \"@comment\"]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, \"comment\", \"@pop\"],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ],\n    // Almost full HTML tag matching, complete with embedded scripts & styles\n    tag: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [\n        /(type)(\\s*=\\s*)(\")([^\"]+)(\")/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [\n        /(type)(\\s*=\\s*)(')([^']+)(')/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [/(\\w+)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, [\"attribute.name.html\", \"delimiter.html\", \"string.html\"]],\n      [/\\w+/, \"attribute.name.html\"],\n      [/\\/>/, \"tag\", \"@pop\"],\n      [\n        />/,\n        {\n          cases: {\n            \"$S2==style\": {\n              token: \"tag\",\n              switchTo: \"embeddedStyle\",\n              nextEmbedded: \"text/css\"\n            },\n            \"$S2==script\": {\n              cases: {\n                $S3: {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"$S3\"\n                },\n                \"@default\": {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"text/javascript\"\n                }\n              }\n            },\n            \"@default\": { token: \"tag\", next: \"@pop\" }\n          }\n        }\n      ]\n    ],\n    embeddedStyle: [\n      [/[^<]+/, \"\"],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ],\n    embeddedScript: [\n      [/[^<]+/, \"\"],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,cAAc,CAAC,QAAQ,KAAK;AAAA,EAC9B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC7C;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,+BAA+B;AAAA,MACjD,KAAK,IAAI,OAAO,kCAAkC;AAAA,IACpD;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA;AAAA,EAEd,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AAAA;AAAA,EAET,WAAW;AAAA;AAAA,EAEX,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ,CAAC,UAAU,YAAY,eAAe;AAAA;AAAA,MAEtC,CAAC,iDAAiD,CAAC,SAAS,WAAW,WAAW,SAAS,CAAC;AAAA;AAAA,MAE5F,CAAC,oBAAoB,SAAS;AAAA;AAAA,MAE9B,CAAC,uBAAuB,gBAAgB;AAAA;AAAA,MAExC,CAAC,UAAU,SAAS;AAAA;AAAA,MAEpB,CAAC,0BAA0B,SAAS;AAAA;AAAA,MAEpC,CAAC,uBAAuB,QAAQ;AAAA;AAAA,MAEhC,CAAC,oCAAoC,EAAE,OAAO,UAAU,MAAM,aAAa,CAAC;AAAA;AAAA,MAE5E;AAAA,QACE;AAAA,QACA,EAAE,OAAO,UAAU,MAAM,gBAAgB,cAAc,KAAK;AAAA,MAC9D;AAAA;AAAA,MAEA,CAAC,eAAe,EAAE,OAAO,UAAU,MAAM,aAAa,CAAC;AAAA;AAAA,MAEvD,EAAE,SAAS,eAAe;AAAA,IAC5B;AAAA,IACA,cAAc;AAAA,MACZ,EAAE,SAAS,gBAAgB;AAAA,MAC3B,CAAC,UAAU,sBAAsB;AAAA;AAAA,IAEnC;AAAA,IACA,YAAY,CAAC,EAAE,SAAS,gBAAgB,GAAG,EAAE,SAAS,eAAe,CAAC;AAAA,IACtE,cAAc;AAAA,MACZ,CAAC,gBAAgB,EAAE,OAAO,WAAW,UAAU,aAAa,CAAC;AAAA;AAAA,MAE7D,CAAC,UAAU,oBAAoB;AAAA;AAAA,MAE/B,CAAC,aAAa,YAAY,MAAM;AAAA;AAAA,MAEhC,CAAC,SAAS,YAAY,MAAM;AAAA;AAAA,MAE5B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA;AAAA,YAER,YAAY;AAAA;AAAA,UAEd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,CAAC,eAAe,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,eAAe,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,OAAO,iBAAiB;AAAA,IAC3B;AAAA;AAAA,IAEA,aAAa;AAAA,MACX,CAAC,WAAW,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MACnE,CAAC,SAAS,iBAAiB;AAAA,IAC7B;AAAA,IACA,aAAa;AAAA;AAAA,MAEX,CAAC,SAAS,eAAe;AAAA,MACzB,CAAC,YAAY,QAAQ;AAAA;AAAA,MAErB,CAAC,qCAAqC,QAAQ;AAAA,MAC9C,CAAC,uCAAuC,QAAQ;AAAA,MAChD,CAAC,eAAe,UAAU;AAAA,MAC1B,CAAC,0BAA0B,UAAU;AAAA,MACrC,CAAC,wBAAwB,UAAU;AAAA;AAAA,MAEnC,CAAC,eAAe,eAAe;AAAA,MAC/B,CAAC,+CAA+C,CAAC,eAAe,IAAI,aAAa,CAAC;AAAA,MAClF,CAAC,qCAAqC,aAAa;AAAA;AAAA,MAEnD,EAAE,SAAS,OAAO;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM;AAAA;AAAA,MAEJ,CAAC,aAAa,KAAK;AAAA,MACnB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,UAAU,EAAE,OAAO,OAAO,MAAM,UAAU;AAAA,YAC1C,YAAY,EAAE,OAAO,OAAO,MAAM,UAAU;AAAA,UAC9C;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,wBAAwB,EAAE,OAAO,MAAM,CAAC;AAAA,MACzC,CAAC,QAAQ,WAAW,UAAU;AAAA,IAChC;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,iBAAiB;AAAA,MAC7B,CAAC,OAAO,WAAW,MAAM;AAAA,MACzB,CAAC,QAAQ,yBAAyB;AAAA,MAClC,CAAC,SAAS,iBAAiB;AAAA,IAC7B;AAAA;AAAA,IAEA,KAAK;AAAA,MACH,CAAC,cAAc,OAAO;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,OAAO,eAAe,UAAU,cAAc;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,OAAO,eAAe,UAAU,cAAc;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,mCAAmC,CAAC,uBAAuB,kBAAkB,aAAa,CAAC;AAAA,MAC5F,CAAC,OAAO,qBAAqB;AAAA,MAC7B,CAAC,OAAO,OAAO,MAAM;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,cACZ,OAAO;AAAA,cACP,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,YACA,eAAe;AAAA,cACb,OAAO;AAAA,gBACL,KAAK;AAAA,kBACH,OAAO;AAAA,kBACP,UAAU;AAAA,kBACV,cAAc;AAAA,gBAChB;AAAA,gBACA,YAAY;AAAA,kBACV,OAAO;AAAA,kBACP,UAAU;AAAA,kBACV,cAAc;AAAA,gBAChB;AAAA,cACF;AAAA,YACF;AAAA,YACA,YAAY,EAAE,OAAO,OAAO,MAAM,OAAO;AAAA,UAC3C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,CAAC,SAAS,EAAE;AAAA,MACZ,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MAC1E,CAAC,KAAK,EAAE;AAAA,IACV;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,SAAS,EAAE;AAAA,MACZ,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MAC3E,CAAC,KAAK,EAAE;AAAA,IACV;AAAA,EACF;AACF;", "names": []}