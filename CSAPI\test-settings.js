// Test script to verify settings API endpoints
const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// Test user credentials (you'll need to replace with actual user token)
const TEST_TOKEN = 'your-jwt-token-here'; // Replace with actual token

const api = axios.create({
  baseURL: API_BASE,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

async function testSettingsAPI() {
  console.log('🧪 Testing Settings API Endpoints...\n');

  try {
    // Test 1: Get default settings schema
    console.log('1️⃣ Testing GET /user/settings/schema');
    const schemaResponse = await api.get('/user/settings/schema');
    console.log('✅ Schema retrieved successfully');
    console.log('   Categories:', Object.keys(schemaResponse.data.schema));
    console.log('   Editor settings count:', Object.keys(schemaResponse.data.schema.editor || {}).length);
    console.log('');

    // Test 2: Get user settings (will create defaults if not exist)
    console.log('2️⃣ Testing GET /user/settings');
    const userSettingsResponse = await api.get('/user/settings');
    console.log('✅ User settings retrieved successfully');
    console.log('   Has defaults:', !!userSettingsResponse.data.defaults);
    console.log('   Settings categories:', Object.keys(userSettingsResponse.data.settings));
    console.log('');

    // Test 3: Update UI settings
    console.log('3️⃣ Testing PATCH /user/settings/ui');
    const uiUpdateData = {
      showFooter: true,
      allowMultiplePanels: false,
      primaryPanelToggleBehavior: 'minimize'
    };
    const uiUpdateResponse = await api.patch('/user/settings/ui', uiUpdateData);
    console.log('✅ UI settings updated successfully');
    console.log('   Updated settings:', Object.keys(uiUpdateData));
    console.log('');

    // Test 4: Update editor settings
    console.log('4️⃣ Testing PATCH /user/settings/editor');
    const editorUpdateData = {
      fontSize: 16,
      theme: 'vs-dark',
      wordWrap: 'off',
      lineNumbers: 'on'
    };
    const editorUpdateResponse = await api.patch('/user/settings/editor', editorUpdateData);
    console.log('✅ Editor settings updated successfully');
    console.log('   Updated settings:', Object.keys(editorUpdateData));
    console.log('');

    // Test 5: Reset UI settings
    console.log('5️⃣ Testing POST /user/settings/reset (UI category)');
    const resetResponse = await api.post('/user/settings/reset', { category: 'ui' });
    console.log('✅ UI settings reset successfully');
    console.log('');

    // Test 6: Get notifications
    console.log('6️⃣ Testing GET /notifications');
    const notificationsResponse = await api.get('/notifications');
    console.log('✅ Notifications retrieved successfully');
    console.log('   Notification count:', notificationsResponse.data.notifications.length);
    console.log('   Unread count:', notificationsResponse.data.unreadCount);
    console.log('');

    console.log('🎉 All tests passed! Settings API is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Test without authentication (should fail)
async function testWithoutAuth() {
  console.log('\n🔒 Testing without authentication (should fail)...');
  
  try {
    const noAuthApi = axios.create({ baseURL: API_BASE });
    await noAuthApi.get('/user/settings');
    console.log('❌ This should have failed!');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly rejected unauthorized request');
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }
}

// Test default settings initialization
async function testDefaultSettings() {
  console.log('\n📋 Testing default settings...');
  
  try {
    // Connect to MongoDB and check default settings
    const mongoose = require('mongoose');
    await mongoose.connect('mongodb://localhost:27017/codespace');
    
    const DefaultSettings = require('./models/DefaultSettings');
    const defaults = await DefaultSettings.getAllDefaults();
    
    console.log('✅ Default settings found in database');
    console.log('   Categories:', Object.keys(defaults));
    console.log('   Editor settings count:', Object.keys(defaults.editor || {}).length);
    console.log('   UI settings count:', Object.keys(defaults.ui || {}).length);
    
    await mongoose.disconnect();
  } catch (error) {
    console.error('❌ Error checking default settings:', error.message);
  }
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting Settings API Tests\n');
  console.log('⚠️  Make sure to:');
  console.log('   1. Start the backend server (npm run dev)');
  console.log('   2. Replace TEST_TOKEN with a valid JWT token');
  console.log('   3. Have a user logged in\n');
  
  await testDefaultSettings();
  await testWithoutAuth();
  
  if (TEST_TOKEN === 'your-jwt-token-here') {
    console.log('\n⚠️  Skipping authenticated tests - please set TEST_TOKEN');
  } else {
    await testSettingsAPI();
  }
}

// Export for use in other files
module.exports = {
  testSettingsAPI,
  testWithoutAuth,
  testDefaultSettings,
  runAllTests
};

// Run if called directly
if (require.main === module) {
  runAllTests();
}
