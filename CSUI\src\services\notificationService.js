import axios from 'axios'

class NotificationService {
  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // Add auth token to requests
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })
  }

  /**
   * Get user notifications
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Notifications response
   */
  async getNotifications(options = {}) {
    try {
      const {
        limit = 20,
        skip = 0,
        unreadOnly = false,
        category = null
      } = options

      const params = new URLSearchParams({
        limit: limit.toString(),
        skip: skip.toString(),
        unreadOnly: unreadOnly.toString()
      })

      if (category) {
        params.append('category', category)
      }

      const response = await this.api.get(`/notifications?${params}`)
      return response.data
    } catch (error) {
      console.error('Error getting notifications:', error)
      throw new Error('Failed to get notifications')
    }
  }

  /**
   * Get unread notification count
   * @returns {Promise<number>} Unread count
   */
  async getUnreadCount() {
    try {
      const response = await this.api.get('/notifications/unread-count')
      return response.data.unreadCount
    } catch (error) {
      console.error('Error getting unread count:', error)
      return 0
    }
  }

  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @returns {Promise<Object>} Updated notification
   */
  async markAsRead(notificationId) {
    try {
      const response = await this.api.patch(`/notifications/${notificationId}/read`)
      return response.data
    } catch (error) {
      console.error('Error marking notification as read:', error)
      throw new Error('Failed to mark notification as read')
    }
  }

  /**
   * Mark all notifications as read
   * @returns {Promise<Object>} Update result
   */
  async markAllAsRead() {
    try {
      const response = await this.api.patch('/notifications/read-all')
      return response.data
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      throw new Error('Failed to mark all notifications as read')
    }
  }

  /**
   * Delete notification
   * @param {string} notificationId - Notification ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteNotification(notificationId) {
    try {
      await this.api.delete(`/notifications/${notificationId}`)
      return true
    } catch (error) {
      console.error('Error deleting notification:', error)
      throw new Error('Failed to delete notification')
    }
  }

  /**
   * Create notification (for testing)
   * @param {Object} notification - Notification data
   * @returns {Promise<Object>} Created notification
   */
  async createNotification(notification) {
    try {
      const response = await this.api.post('/notifications', notification)
      return response.data
    } catch (error) {
      console.error('Error creating notification:', error)
      throw new Error('Failed to create notification')
    }
  }

  /**
   * Get notifications by category
   * @param {string} category - Notification category
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Notifications response
   */
  async getNotificationsByCategory(category, options = {}) {
    try {
      return await this.getNotifications({
        ...options,
        category
      })
    } catch (error) {
      console.error(`Error getting ${category} notifications:`, error)
      throw new Error(`Failed to get ${category} notifications`)
    }
  }

  /**
   * Get settings notifications
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Settings notifications
   */
  async getSettingsNotifications(options = {}) {
    return this.getNotificationsByCategory('settings', options)
  }

  /**
   * Show browser notification (if permission granted)
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {Object} options - Notification options
   */
  showBrowserNotification(title, message, options = {}) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(title, {
        body: message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options
      })

      // Auto close after 5 seconds
      setTimeout(() => {
        notification.close()
      }, 5000)

      return notification
    }
  }

  /**
   * Request browser notification permission
   * @returns {Promise<string>} Permission status
   */
  async requestNotificationPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission
    }
    return 'denied'
  }

  /**
   * Check if browser notifications are supported and permitted
   * @returns {boolean} Support status
   */
  isBrowserNotificationSupported() {
    return 'Notification' in window && Notification.permission === 'granted'
  }

  /**
   * Format notification for display
   * @param {Object} notification - Raw notification object
   * @returns {Object} Formatted notification
   */
  formatNotification(notification) {
    return {
      id: notification._id,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      category: notification.category,
      priority: notification.priority,
      isRead: notification.isRead,
      readAt: notification.readAt,
      createdAt: notification.createdAt,
      data: notification.data || {},
      
      // Helper properties
      isRecent: new Date() - new Date(notification.createdAt) < 24 * 60 * 60 * 1000, // Less than 24 hours
      timeAgo: this.getTimeAgo(notification.createdAt),
      priorityColor: this.getPriorityColor(notification.priority),
      categoryIcon: this.getCategoryIcon(notification.category)
    }
  }

  /**
   * Get time ago string
   * @param {string} dateString - Date string
   * @returns {string} Time ago string
   */
  getTimeAgo(dateString) {
    const now = new Date()
    const date = new Date(dateString)
    const diffInSeconds = Math.floor((now - date) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  /**
   * Get priority color
   * @param {string} priority - Priority level
   * @returns {string} Color class
   */
  getPriorityColor(priority) {
    const colors = {
      low: 'text-gray-400',
      medium: 'text-blue-400',
      high: 'text-yellow-400',
      urgent: 'text-red-400'
    }
    return colors[priority] || colors.medium
  }

  /**
   * Get category icon
   * @param {string} category - Category name
   * @returns {string} Icon emoji
   */
  getCategoryIcon(category) {
    const icons = {
      settings: '⚙️',
      profile: '👤',
      codespace: '💻',
      collaboration: '👥',
      system: '🔧'
    }
    return icons[category] || '📢'
  }
}

// Create and export service instance
const notificationService = new NotificationService()

export default notificationService

// Export individual methods for convenience
export const {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  createNotification,
  getNotificationsByCategory,
  getSettingsNotifications,
  showBrowserNotification,
  requestNotificationPermission,
  isBrowserNotificationSupported,
  formatNotification
} = notificationService
