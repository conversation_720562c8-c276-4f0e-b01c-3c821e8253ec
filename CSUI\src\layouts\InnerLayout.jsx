import React, { useState, useEffect, useRef } from 'react'
import { Outlet, Link, useNavigate, useLocation } from 'react-router-dom'
import useCodespaceStore from '../store/codespaceStore'
import NewCodespaceModal from '../components/codespace/NewCodespaceModal'

const InnerLayout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);
  const [newCodespaceModalOpen, setNewCodespaceModalOpen] = useState(false);
  const profileDropdownRef = useRef(null);

  const {
    codespaces,
    fetchCodespaces,
    isLoading
  } = useCodespaceStore();

  // Fetch codespaces when component mounts
  useEffect(() => {
    fetchCodespaces();
  }, [fetchCodespaces]);

  // <PERSON>le click outside of profile dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target)) {
        setProfileDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    navigate('/', { replace: true });
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleProfileDropdown = () => {
    setProfileDropdownOpen(!profileDropdownOpen);
  };

  return (
    <div className="min-h-screen bg-gray-900 flex relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -inset-[10%] bg-gradient-to-tr from-green-500/5 via-green-300/10 to-transparent rotate-12 transform-gpu blur-3xl"></div>
        <div className="absolute -inset-[5%] top-[30%] bg-gradient-to-bl from-green-500/5 via-green-400/5 to-transparent -rotate-12 transform-gpu blur-2xl"></div>
      </div>

      {/* Left Sidebar - Collapsible */}
      <aside
        className={`${sidebarCollapsed ? 'w-16' : 'w-64'} bg-gray-800/80 backdrop-blur-sm border-r border-gray-700/50 text-white relative z-10 transition-all duration-300 ease-in-out flex flex-col`}
      >
        {/* Logo and toggle button */}
        <div className="p-4 flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-green-500 w-8 h-8 rounded-lg flex items-center justify-center shadow-lg shadow-green-500/20">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
            </div>
            {!sidebarCollapsed && <h1 className="text-xl font-bold ml-3">CodeSpace</h1>}
          </div>
          <button
            onClick={toggleSidebar}
            className="text-gray-400 hover:text-white transition-colors"
          >
            {sidebarCollapsed ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
              </svg>
            )}
          </button>
        </div>

        {/* New Codespace Button */}
        <div className="px-4 py-2">
          <button
            className="w-full bg-green-600 hover:bg-green-700 text-white rounded-lg py-2 px-3 flex items-center justify-center transition-colors duration-200"
            onClick={() => setNewCodespaceModalOpen(true)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            {!sidebarCollapsed && <span>New Codespace</span>}
          </button>
        </div>

        {/* Recent Codespaces */}
        <div className="flex-grow overflow-y-auto mt-4">
          <div className={`px-4 ${sidebarCollapsed ? 'hidden' : 'block'}`}>
            <h2 className="text-xs uppercase tracking-wider text-gray-400 mb-2">Recent Codespaces</h2>
          </div>

          {isLoading ? (
            <div className="py-4 px-4 text-center">
              <div className="w-6 h-6 border-2 border-gray-600 border-t-green-500 rounded-full animate-spin mx-auto"></div>
              <p className="text-xs text-gray-500 mt-2">Loading...</p>
            </div>
          ) : codespaces.length > 0 ? (
            <div className="space-y-1">
              {codespaces.map(space => (
                <Link
                  key={space._id}
                  to={`/codespace/${space._id}`}
                  className={`flex items-center py-2 px-4 hover:bg-gray-700/70 hover:text-green-400 transition-colors duration-200 ${
                    location.pathname === `/codespace/${space._id}` ? 'bg-gray-700/70 text-green-400' : ''
                  }`}
                >
                  {/* Language icon - simplified for now */}
                  <div className="w-6 h-6 rounded bg-gray-700 flex items-center justify-center text-xs text-green-400 mr-3">
                    {space.files && space.files.length > 0
                      ? space.files[0].language.charAt(0).toUpperCase()
                      : 'C'}
                  </div>

                  {!sidebarCollapsed && (
                    <div className="truncate">
                      <div className="font-medium truncate">{space.name}</div>
                      <div className="text-xs text-gray-400">
                        Last edited: {new Date(space.lastAccessed).toLocaleDateString()}
                        {space.isPublic && <span className="ml-2 text-green-400 text-xs">Public</span>}
                      </div>
                    </div>
                  )}
                </Link>
              ))}
            </div>
          ) : (
            <div className="py-4 px-4 text-center">
              <p className="text-sm text-gray-400">No codespaces yet</p>
              <button
                className="mt-2 text-xs text-green-400 hover:text-green-300"
                onClick={() => setNewCodespaceModalOpen(true)}
              >
                Create your first codespace
              </button>
            </div>
          )}
        </div>

        {/* Bottom section with create new button */}
        <div className="p-4 border-t border-gray-700/50">
          <button
            className="w-full flex items-center justify-center py-2 px-4 rounded-lg hover:bg-gray-700/70 text-gray-300 hover:text-green-400 transition-colors duration-200"
            onClick={() => setNewCodespaceModalOpen(true)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            {!sidebarCollapsed && <span className="ml-2">Create New</span>}
          </button>
        </div>
      </aside>

      {/* Main content area */}
      <div className="flex-grow flex flex-col">
        {/* Top navigation bar */}
        <header className="h-14 bg-gray-800/80 backdrop-blur-sm border-b border-gray-700/50 flex items-center justify-between px-4 relative z-10">
          <div className="flex items-center">
            <h2 className="text-white font-medium">Dashboard</h2>
          </div>

          {/* Search bar - can be expanded later */}
          <div className="flex-grow max-w-2xl mx-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search codespaces..."
                className="w-full bg-gray-700/50 border border-gray-600 rounded-lg py-1.5 px-4 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-green-500"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* Profile dropdown */}
          <div className="relative" ref={profileDropdownRef}>
            <button
              onClick={toggleProfileDropdown}
              className="flex items-center space-x-2 focus:outline-none"
            >
              <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center text-gray-900 font-medium">
                U
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${profileDropdownOpen ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>

            {/* Dropdown menu */}
            {profileDropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-lg py-1 z-dropdown">
                <Link to="/profile" className="block px-4 py-2 text-white hover:bg-gray-700 hover:text-green-400">
                  Profile
                </Link>
                <Link to="/settings" className="block px-4 py-2 text-white hover:bg-gray-700 hover:text-green-400">
                  Settings
                </Link>
                <Link to="/change-password" className="block px-4 py-2 text-white hover:bg-gray-700 hover:text-green-400">
                  Change Password
                </Link>
                <div className="border-t border-gray-700 my-1"></div>
                <button
                  onClick={handleLogout}
                  className="block w-full text-left px-4 py-2 text-red-400 hover:bg-gray-700 hover:text-red-300"
                >
                  Logout
                </button>
              </div>
            )}
          </div>
        </header>

        {/* Main content and right sidebar */}
        <div className="flex-grow flex">
          {/* Main content area */}
          <main className="flex-grow bg-gray-900/80 backdrop-blur-sm text-white relative z-10 overflow-auto">
            <Outlet />
          </main>

          {/* Right sidebar */}
          <aside className="w-64 bg-gray-800/80 backdrop-blur-sm border-l border-gray-700/50 text-white p-4 relative z-10">
            <h3 className="text-lg font-medium mb-4">Tools</h3>

            {/* Placeholder for future tools */}
            <div className="space-y-3">
              <button className="w-full flex items-center justify-between py-2 px-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors">
                <span className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Run Code
                </span>
                <span className="text-xs text-gray-400">Ctrl+Enter</span>
              </button>

              <button className="w-full flex items-center justify-between py-2 px-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors">
                <span className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                  </svg>
                  Save
                </span>
                <span className="text-xs text-gray-400">Ctrl+S</span>
              </button>

              <button className="w-full flex items-center justify-between py-2 px-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors">
                <span className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                  </svg>
                  Share
                </span>
              </button>
            </div>

            {/* Language selector */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-400 mb-2">Language</label>
              <select className="w-full bg-gray-700 border border-gray-600 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-green-500">
                <option>JavaScript</option>
                <option>Python</option>
                <option>Java</option>
                <option>C++</option>
                <option>HTML</option>
                <option>CSS</option>
              </select>
            </div>

            {/* Settings section */}
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-400 mb-2">Editor Settings</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Theme</span>
                  <select className="bg-gray-700 border border-gray-600 rounded py-1 px-2 text-sm text-white focus:outline-none focus:ring-1 focus:ring-green-500">
                    <option>Dark</option>
                    <option>Light</option>
                  </select>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Font Size</span>
                  <select className="bg-gray-700 border border-gray-600 rounded py-1 px-2 text-sm text-white focus:outline-none focus:ring-1 focus:ring-green-500">
                    <option>12px</option>
                    <option>14px</option>
                    <option>16px</option>
                    <option>18px</option>
                  </select>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" id="wrap" className="mr-2" />
                  <label htmlFor="wrap" className="text-sm">Word Wrap</label>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>

      {/* New Codespace Modal */}
      <NewCodespaceModal
        isOpen={newCodespaceModalOpen}
        onClose={() => setNewCodespaceModalOpen(false)}
      />
    </div>
  )
}

export default InnerLayout