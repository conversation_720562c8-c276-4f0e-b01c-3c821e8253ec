import React, { useState, useEffect } from 'react'
import { Outlet } from 'react-router-dom'
import TopNavbar from '../components/layout/TopNavbar'
import LeftSidebar from '../components/layout/LeftSidebar'
import RightSidebar from '../components/layout/RightSidebar'
import Footer from '../components/layout/Footer'
import useCodespaceStore from '../store/codespaceStore'
import NewCodespaceModal from '../components/codespace/NewCodespaceModal'

const InnerLayout = () => {
  const [leftSidebarCollapsed, setLeftSidebarCollapsed] = useState(false)
  const [rightSidebarCollapsed, setRightSidebarCollapsed] = useState(false)
  const [newCodespaceModalOpen, setNewCodespaceModalOpen] = useState(false)

  const {
    fetchCodespaces
  } = useCodespaceStore()

  // Fetch codespaces when component mounts
  useEffect(() => {
    fetchCodespaces()
  }, [fetchCodespaces])

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setLeftSidebarCollapsed(true)
        setRightSidebarCollapsed(true)
      } else if (window.innerWidth < 1280) {
        setRightSidebarCollapsed(true)
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -inset-[10%] bg-gradient-to-tr from-green-500/5 via-green-300/10 to-transparent rotate-12 transform-gpu blur-3xl"></div>
        <div className="absolute -inset-[5%] top-[30%] bg-gradient-to-bl from-green-500/5 via-green-400/5 to-transparent -rotate-12 transform-gpu blur-2xl"></div>
      </div>

      {/* Top Navigation */}
      <TopNavbar />

      {/* Main Layout */}
      <div className="flex flex-1 relative z-10">
        {/* Left Sidebar */}
        <LeftSidebar
          isCollapsed={leftSidebarCollapsed}
          onToggleCollapse={() => setLeftSidebarCollapsed(!leftSidebarCollapsed)}
        />

        {/* Main Content Area */}
        <main className="flex-1 flex flex-col min-w-0">
          <div className="flex-1 bg-gray-900/80 backdrop-blur-sm text-white overflow-auto">
            <Outlet />
          </div>
        </main>

        {/* Right Sidebar */}
        <RightSidebar
          isCollapsed={rightSidebarCollapsed}
          onToggleCollapse={() => setRightSidebarCollapsed(!rightSidebarCollapsed)}
        />
      </div>

      {/* Footer */}
      <Footer />

      {/* Modals */}
      {newCodespaceModalOpen && (
        <NewCodespaceModal
          isOpen={newCodespaceModalOpen}
          onClose={() => setNewCodespaceModalOpen(false)}
        />
      )}
    </div>
  )
}

export default InnerLayout