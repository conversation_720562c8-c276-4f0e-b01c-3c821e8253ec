const Notification = require('../models/Notification');

// @desc    Get user notifications
// @route   GET /api/notifications
// @access  Private
exports.getNotifications = async (req, res) => {
  try {
    const {
      limit = 20,
      skip = 0,
      unreadOnly = false,
      category = null
    } = req.query;

    const notifications = await Notification.getUserNotifications(req.user.id, {
      limit: parseInt(limit),
      skip: parseInt(skip),
      unreadOnly: unreadOnly === 'true',
      category
    });

    const unreadCount = await Notification.getUnreadCount(req.user.id);

    res.status(200).json({
      success: true,
      notifications,
      unreadCount,
      total: notifications.length
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Mark notification as read
// @route   PATCH /api/notifications/:id/read
// @access  Private
exports.markAsRead = async (req, res) => {
  try {
    const notification = await Notification.markAsRead(req.params.id, req.user.id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    res.status(200).json({
      success: true,
      notification,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Mark all notifications as read
// @route   PATCH /api/notifications/read-all
// @access  Private
exports.markAllAsRead = async (req, res) => {
  try {
    const result = await Notification.markAllAsRead(req.user.id);

    res.status(200).json({
      success: true,
      modifiedCount: result.modifiedCount,
      message: 'All notifications marked as read'
    });
  } catch (error) {
    console.error('Mark all notifications as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get unread notification count
// @route   GET /api/notifications/unread-count
// @access  Private
exports.getUnreadCount = async (req, res) => {
  try {
    const count = await Notification.getUnreadCount(req.user.id);

    res.status(200).json({
      success: true,
      unreadCount: count
    });
  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Delete notification
// @route   DELETE /api/notifications/:id
// @access  Private
exports.deleteNotification = async (req, res) => {
  try {
    const notification = await Notification.findOneAndDelete({
      _id: req.params.id,
      userId: req.user.id
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Create notification (for testing/admin use)
// @route   POST /api/notifications
// @access  Private
exports.createNotification = async (req, res) => {
  try {
    const { type, title, message, category, priority, data } = req.body;

    const notification = await Notification.createNotification(
      req.user.id,
      type,
      title,
      message,
      {
        category,
        priority,
        data
      }
    );

    res.status(201).json({
      success: true,
      notification,
      message: 'Notification created successfully'
    });
  } catch (error) {
    console.error('Create notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// Helper function to create settings-related notifications
exports.createSettingsNotification = async (userId, action, category, details = {}) => {
  try {
    let title, message, type;

    switch (action) {
      case 'saved':
        title = 'Settings Saved';
        message = `Your ${category} settings have been saved successfully.`;
        type = 'settings_saved';
        break;
      case 'reset':
        title = 'Settings Reset';
        message = `Your ${category} settings have been reset to defaults.`;
        type = 'settings_reset';
        break;
      case 'imported':
        title = 'Settings Imported';
        message = `Settings have been imported successfully.`;
        type = 'settings_imported';
        break;
      case 'exported':
        title = 'Settings Exported';
        message = `Your settings have been exported successfully.`;
        type = 'settings_exported';
        break;
      default:
        title = 'Settings Updated';
        message = `Your settings have been updated.`;
        type = 'settings_saved';
    }

    const notification = await Notification.createNotification(
      userId,
      type,
      title,
      message,
      {
        category: 'settings',
        priority: 'low',
        data: {
          settingsCategory: category,
          action,
          ...details
        }
      }
    );

    return notification;
  } catch (error) {
    console.error('Error creating settings notification:', error);
    // Don't throw error to avoid breaking the main operation
    return null;
  }
};

module.exports = exports;
