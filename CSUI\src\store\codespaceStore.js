import { create } from 'zustand';
import * as codespaceService from '../services/codespaceService';

const useCodespaceStore = create((set, get) => ({
  // State
  codespaces: [],
  currentCodespace: null,
  isLoading: false,
  error: null,

  // Actions
  /**
   * Fetch all codespaces for the current user
   */
  fetchCodespaces: async () => {
    try {
      set({ isLoading: true, error: null });
      const response = await codespaceService.getMyCodespaces();
      set({
        isLoading: false,
        codespaces: response.codespaces || []
      });
      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  /**
   * Fetch a single codespace by ID
   * @param {string} id - Codespace ID
   */
  fetchCodespace: async (id) => {
    try {
      set({ isLoading: true, error: null });
      const response = await codespaceService.getCodespace(id);
      set({
        isLoading: false,
        currentCodespace: response.codespace
      });
      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  /**
   * Fetch a codespace by access code (for public codespaces)
   * @param {string} accessCode - Codespace access code
   */
  fetchCodespaceByAccessCode: async (accessCode) => {
    try {
      set({ isLoading: true, error: null });
      const response = await codespaceService.getCodespaceByAccessCode(accessCode);
      set({
        isLoading: false,
        currentCodespace: response.codespace
      });
      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  /**
   * Fetch a codespace by slug (for public codespaces)
   * @param {string} slug - Codespace slug
   */
  fetchCodespaceBySlug: async (slug) => {
    try {
      set({ isLoading: true, error: null });
      const response = await codespaceService.getCodespaceBySlug(slug);
      set({
        isLoading: false,
        currentCodespace: response.codespace
      });
      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  /**
   * Create a new codespace
   * @param {Object} codespaceData - Codespace data
   */
  createCodespace: async (codespaceData) => {
    try {
      set({ isLoading: true, error: null });
      const response = await codespaceService.createCodespace(codespaceData);

      // Add the new codespace to the list
      set(state => ({
        isLoading: false,
        codespaces: [response.codespace, ...state.codespaces]
      }));

      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  /**
   * Update a codespace
   * @param {string} id - Codespace ID
   * @param {Object} codespaceData - Updated codespace data
   */
  updateCodespace: async (id, codespaceData) => {
    try {
      set({ isLoading: true, error: null });
      const response = await codespaceService.updateCodespace(id, codespaceData);

      // Update the codespace in the list
      set(state => ({
        isLoading: false,
        codespaces: state.codespaces.map(cs =>
          cs._id === id ? response.codespace : cs
        ),
        currentCodespace: state.currentCodespace?._id === id
          ? response.codespace
          : state.currentCodespace
      }));

      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  /**
   * Delete a codespace
   * @param {string} id - Codespace ID
   */
  deleteCodespace: async (id) => {
    try {
      set({ isLoading: true, error: null });
      const response = await codespaceService.deleteCodespace(id);

      // Remove the codespace from the list
      set(state => ({
        isLoading: false,
        codespaces: state.codespaces.filter(cs => cs._id !== id),
        currentCodespace: state.currentCodespace?._id === id
          ? null
          : state.currentCodespace
      }));

      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  /**
   * Update a file in the current codespace
   * @param {string} fileId - File ID
   * @param {Object} fileData - Updated file data
   */
  updateFile: async (fileId, fileData) => {
    try {
      const { currentCodespace } = get();
      if (!currentCodespace) throw new Error('No codespace selected');

      set({ isLoading: true, error: null });
      const response = await codespaceService.updateCodespaceFile(
        currentCodespace._id,
        fileId,
        fileData
      );

      // Update the file in the current codespace
      set(state => {
        const updatedFiles = state.currentCodespace.files.map(file =>
          file._id === fileId ? response.file : file
        );

        return {
          isLoading: false,
          currentCodespace: {
            ...state.currentCodespace,
            files: updatedFiles
          }
        };
      });

      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  /**
   * Add a new file to the current codespace
   * @param {Object} fileData - File data
   */
  addFile: async (fileData) => {
    try {
      const { currentCodespace } = get();
      if (!currentCodespace) throw new Error('No codespace selected');

      set({ isLoading: true, error: null });
      const response = await codespaceService.addCodespaceFile(
        currentCodespace._id,
        fileData
      );

      // Add the new file to the current codespace
      set(state => ({
        isLoading: false,
        currentCodespace: {
          ...state.currentCodespace,
          files: [...state.currentCodespace.files, response.file]
        }
      }));

      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  /**
   * Delete a file from the current codespace
   * @param {string} fileId - File ID
   */
  deleteFile: async (fileId) => {
    try {
      const { currentCodespace } = get();
      if (!currentCodespace) throw new Error('No codespace selected');

      set({ isLoading: true, error: null });
      const response = await codespaceService.deleteCodespaceFile(
        currentCodespace._id,
        fileId
      );

      // Remove the file from the current codespace
      set(state => ({
        isLoading: false,
        currentCodespace: {
          ...state.currentCodespace,
          files: state.currentCodespace.files.filter(file => file._id !== fileId)
        }
      }));

      return response;
    } catch (error) {
      set({ isLoading: false, error: error.message });
      throw error;
    }
  },

  // Reset state
  resetCodespaceState: () => {
    set({
      codespaces: [],
      currentCodespace: null,
      isLoading: false,
      error: null
    });
  }
}));

export default useCodespaceStore;
