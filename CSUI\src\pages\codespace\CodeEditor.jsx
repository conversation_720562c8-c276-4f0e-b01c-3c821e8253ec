import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import useCodespaceStore from '../../store/codespaceStore'

const CodeEditor = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const {
    currentCodespace,
    fetchCodespace,
    updateFile,
    isLoading,
    error
  } = useCodespaceStore();

  const [activeFileId, setActiveFileId] = useState(null);
  const [code, setCode] = useState('');
  const [output, setOutput] = useState('');

  // Fetch codespace data when component mounts or ID changes
  useEffect(() => {
    const loadCodespace = async () => {
      try {
        // If we have an ID in the URL, fetch that specific codespace
        if (id) {
          await fetchCodespace(id);
        } else {
          // For the dashboard route, we could either:
          // 1. Create a new temporary codespace
          // 2. Redirect to a "create new" page
          // 3. Show a list of recent codespaces
          // For now, we'll just use a default code
          setCode(`// Welcome to CodeSpace Editor

function greet(name) {
  return 'Hello, ' + name + '!';
}

console.log(greet('World'));`);
        }
      } catch (err) {
        console.error('Error loading codespace:', err);
        // Handle error - maybe redirect or show error message
      }
    };

    loadCodespace();
  }, [id, fetchCodespace]);

  // Set active file when codespace loads
  useEffect(() => {
    if (currentCodespace && currentCodespace.files && currentCodespace.files.length > 0) {
      const firstFile = currentCodespace.files[0];
      setActiveFileId(firstFile._id);
      setCode(firstFile.content);
    }
  }, [currentCodespace]);

  // Get the active file
  const activeFile = currentCodespace?.files?.find(file => file._id === activeFileId);

  const handleCodeChange = (e) => {
    setCode(e.target.value);

    // In a real app, you might want to debounce this or save on explicit action
    if (currentCodespace && activeFileId) {
      updateFile(activeFileId, { content: e.target.value });
    }
  };

  const handleRunCode = () => {
    try {
      // This is a simple implementation that only works for JavaScript
      // In a real app, you would send this to the server for execution
      const result = new Function(code)();
      setOutput(String(result || 'Code executed successfully'));
    } catch (err) {
      setOutput(`Error: ${err.message}`);
    }
  };

  // Loading state
  if (isLoading && id) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-gray-600 border-t-green-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Loading codespace...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error && id) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-5xl mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-white mb-2">Error Loading Codespace</h2>
          <p className="text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col p-4">
      {/* Codespace title */}
      {currentCodespace && (
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-white">
            {currentCodespace.name}
            {currentCodespace.isPublic && (
              <span className="ml-2 text-xs bg-green-600 text-white px-2 py-1 rounded-full">Public</span>
            )}
          </h2>
          {currentCodespace.description && (
            <p className="text-gray-400 text-sm">{currentCodespace.description}</p>
          )}
        </div>
      )}

      {/* File tabs */}
      <div className="flex border-b border-gray-700">
        {currentCodespace?.files?.map(file => (
          <div
            key={file._id}
            className={`px-4 py-2 border-r border-gray-700 flex items-center cursor-pointer ${
              activeFileId === file._id ? 'bg-gray-800 text-white' : 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
            }`}
            onClick={() => {
              setActiveFileId(file._id);
              setCode(file.content);
            }}
          >
            <span className="font-medium">{file.name}</span>
            {currentCodespace.files.length > 1 && (
              <button
                className="ml-2 text-gray-400 hover:text-gray-300"
                onClick={(e) => {
                  e.stopPropagation();
                  // Handle file deletion (would need to implement this)
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        ))}

        {/* If no codespace is loaded, show a default tab */}
        {!currentCodespace && (
          <div className="px-4 py-2 bg-gray-800 border-r border-gray-700 flex items-center text-white">
            <span className="font-medium">main.js</span>
          </div>
        )}

        <button
          className="px-3 py-2 text-gray-400 hover:text-white flex items-center"
          onClick={() => {
            // Handle adding a new file (would need to implement this)
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
      </div>

      {/* Code editor */}
      <div className="flex-grow flex flex-col">
        <div className="flex-grow border border-gray-700 bg-gray-800/70 backdrop-blur-sm">
          <textarea
            className="w-full h-full p-4 font-mono text-sm bg-transparent text-gray-200 focus:outline-none resize-none"
            placeholder="// Write your code here"
            value={code}
            onChange={handleCodeChange}
          ></textarea>
        </div>

        {/* Output panel */}
        <div className="h-48 mt-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-gray-300">Output</h3>
            <div className="flex space-x-2">
              <button
                className="text-xs px-2 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded"
                onClick={() => setOutput('')}
              >
                Clear
              </button>
              <button
                className="text-xs px-2 py-1 bg-green-600 hover:bg-green-700 text-white rounded"
                onClick={handleRunCode}
              >
                Run
              </button>
            </div>
          </div>
          <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm h-full overflow-y-auto border border-gray-700">
            {output ? (
              <p>{output}</p>
            ) : (
              <p className="text-gray-500">// Output will appear here after running your code</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CodeEditor