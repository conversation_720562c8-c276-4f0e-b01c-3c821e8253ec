import React, { useState, useEffect, useRef } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import useCodespaceStore from '../../store/codespaceStore'
import Terminal from '../../components/codespace/Terminal'
import EditorSidebar from '../../components/codespace/EditorSidebar'

const CodeEditor = () => {
  const { id, code: accessCode } = useParams();
  const navigate = useNavigate();
  const {
    currentCodespace,
    fetchCodespace,
    fetchCodespaceByAccessCode,
    updateCodespace,
    updateFile,
    addFile,
    deleteFile,
    isLoading,
    error
  } = useCodespaceStore();

  const [activeFileId, setActiveFileId] = useState(null);
  const [code, setCode] = useState('');
  const [output, setOutput] = useState('');
  const [isTerminalOpen, setIsTerminalOpen] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  const [showShareTooltip, setShowShareTooltip] = useState(false);
  const editorRef = useRef(null);

  // Fetch codespace data when component mounts or ID/accessCode changes
  useEffect(() => {
    const loadCodespace = async () => {
      try {
        // If we have an access code in the URL, fetch that specific public codespace
        if (accessCode) {
          await fetchCodespaceByAccessCode(accessCode);
        }
        // If we have an ID in the URL, fetch that specific codespace
        else if (id) {
          await fetchCodespace(id);
        } else {
          // For the dashboard route, we could either:
          // 1. Create a new temporary codespace
          // 2. Redirect to a "create new" page
          // 3. Show a list of recent codespaces
          // For now, we'll just use a default code
          setCode(`// Welcome to CodeSpace Editor

function greet(name) {
  return 'Hello, ' + name + '!';
}

console.log(greet('World'));`);
        }
      } catch (err) {
        console.error('Error loading codespace:', err);
        // Handle error - maybe redirect or show error message
      }
    };

    loadCodespace();
  }, [id, accessCode, fetchCodespace, fetchCodespaceByAccessCode]);

  // Add keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl+S for save
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        handleSaveFile();
      }

      // Ctrl+Enter for run
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        handleRunCode();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentCodespace, activeFileId, code]); // Re-add event listener when these dependencies change

  // Set active file when codespace loads
  useEffect(() => {
    if (currentCodespace && currentCodespace.files && currentCodespace.files.length > 0) {
      const firstFile = currentCodespace.files[0];
      setActiveFileId(firstFile._id);
      setCode(firstFile.content);

      // Generate share URL if codespace is public
      if (currentCodespace.isPublic && currentCodespace.accessCode) {
        const baseUrl = window.location.origin;
        setShareUrl(`${baseUrl}/codespace/access/${currentCodespace.accessCode}`);
      } else {
        setShareUrl('');
      }
    }
  }, [currentCodespace]);

  // Handle share button click
  const handleShare = () => {
    if (!currentCodespace) return;

    if (currentCodespace.isPublic && shareUrl) {
      // Copy to clipboard
      navigator.clipboard.writeText(shareUrl)
        .then(() => {
          setShowShareTooltip(true);
          setTimeout(() => setShowShareTooltip(false), 2000);
        })
        .catch(err => console.error('Failed to copy URL: ', err));
    } else {
      // Make codespace public
      updateCodespace(currentCodespace._id, { isPublic: true })
        .then(() => {
          // URL will be set in the useEffect when codespace updates
        })
        .catch(err => console.error('Failed to make codespace public: ', err));
    }
  };

  // Get the active file
  const activeFile = currentCodespace?.files?.find(file => file._id === activeFileId);

  // Debounce function for auto-saving
  const debounce = (func, delay) => {
    let timeoutId;
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  // Save file content with debounce
  const saveFileContent = debounce((fileId, content) => {
    if (currentCodespace && fileId) {
      updateFile(fileId, { content });
    }
  }, 1000); // 1 second delay

  // Handle code changes
  const handleCodeChange = (e) => {
    const newCode = e.target.value;
    setCode(newCode);

    // Auto-save after a delay
    if (currentCodespace && activeFileId) {
      saveFileContent(activeFileId, newCode);
    }
  };

  // Manual save function
  const handleSaveFile = () => {
    if (currentCodespace && activeFileId) {
      updateFile(activeFileId, { content: code });

      // Show a temporary save notification
      const saveButton = document.getElementById('save-button');
      if (saveButton) {
        const originalText = saveButton.innerText;
        saveButton.innerText = 'Saved!';
        saveButton.classList.add('bg-green-700');

        setTimeout(() => {
          saveButton.innerText = originalText;
          saveButton.classList.remove('bg-green-700');
        }, 2000);
      }
    }
  };

  const handleRunCode = () => {
    try {
      setIsTerminalOpen(true);

      // Get the language from the active file or default to JavaScript
      const language = activeFile?.language || 'javascript';

      let result = '';

      // Execute code based on language
      if (language === 'javascript') {
        try {
          // This is a simple implementation that only works for JavaScript
          // In a real app, you would send this to the server for execution
          const timestamp = new Date().toLocaleTimeString();
          setOutput(`$ Running JavaScript code... [${timestamp}]\n`);

          // Use setTimeout to simulate server processing
          setTimeout(() => {
            try {
              const execResult = new Function(code)();
              setOutput(prev => prev + `\n${String(execResult || 'Code executed successfully')}\n\n$ Process completed successfully.`);
            } catch (execErr) {
              setOutput(prev => prev + `\n\nError: ${execErr.message}\n\n$ Process completed with errors.`);
            }
          }, 500);
        } catch (jsErr) {
          setOutput(`Error: ${jsErr.message}`);
        }
      } else if (language === 'python') {
        // Simulate Python execution
        const timestamp = new Date().toLocaleTimeString();
        setOutput(`$ Running Python code... [${timestamp}]\n\n# This is a simulation of Python execution\n# In a real app, this would be sent to a server\n\n`);

        // Simulate output after a delay
        setTimeout(() => {
          if (code.includes('print')) {
            const printMatches = code.match(/print\(['"](.+?)['"]\)/g);
            if (printMatches) {
              const outputs = printMatches.map(match => {
                const content = match.match(/print\(['"](.+?)['"]\)/);
                return content ? content[1] : '';
              });
              setOutput(prev => prev + outputs.join('\n') + '\n\n$ Process completed successfully.');
            }
          } else {
            setOutput(prev => prev + '$ Process completed successfully.');
          }
        }, 800);
      } else {
        // For other languages
        setOutput(`$ Running ${language} code is not supported in this demo.\n$ In a real application, this would be sent to a server for execution.`);
      }
    } catch (err) {
      setOutput(`Error: ${err.message}`);
    }
  };

  // Loading state
  if (isLoading && id) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-gray-600 border-t-green-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Loading codespace...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error && id) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-5xl mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-white mb-2">Error Loading Codespace</h2>
          <p className="text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // Handle language change
  const handleLanguageChange = (language) => {
    if (currentCodespace && activeFileId) {
      updateFile(activeFileId, { language });
    }
  };

  // Handle file deletion
  const handleDeleteFile = async (fileId, e) => {
    if (e) e.stopPropagation();

    if (!currentCodespace || !fileId) return;

    // Confirm deletion
    if (!window.confirm('Are you sure you want to delete this file?')) return;

    try {
      await deleteFile(fileId);

      // If the deleted file was active, select another file
      if (activeFileId === fileId && currentCodespace.files.length > 0) {
        const nextFile = currentCodespace.files[0];
        setActiveFileId(nextFile._id);
        setCode(nextFile.content);
      }
    } catch (err) {
      console.error('Error deleting file:', err);
    }
  };

  // Handle new file creation
  const handleAddFile = async () => {
    if (!currentCodespace) return;

    // Prompt for file name
    const fileName = window.prompt('Enter file name (include extension):');
    if (!fileName) return;

    // Determine language from file extension
    const extension = fileName.split('.').pop().toLowerCase();
    let language = 'text';

    // Map common extensions to languages
    const extensionMap = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'html': 'html',
      'css': 'css',
      'json': 'json',
      'md': 'markdown',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'go': 'go',
      'rb': 'ruby',
      'php': 'php'
    };

    if (extensionMap[extension]) {
      language = extensionMap[extension];
    }

    try {
      const response = await addFile({
        name: fileName,
        language,
        content: `// New ${language} file created on ${new Date().toLocaleDateString()}`
      });

      // Select the new file
      setActiveFileId(response.file._id);
      setCode(response.file.content);
    } catch (err) {
      console.error('Error adding file:', err);
    }
  };

  return (
    <div className="h-full flex flex-col p-4">
      {/* Codespace title */}
      {currentCodespace && (
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-white">
            {currentCodespace.name}
            {currentCodespace.isPublic && (
              <span className="ml-2 text-xs bg-green-600 text-white px-2 py-1 rounded-full">Public</span>
            )}
          </h2>
          {currentCodespace.description && (
            <p className="text-gray-400 text-sm">{currentCodespace.description}</p>
          )}
        </div>
      )}

      {/* File tabs */}
      <div className="flex border-b border-gray-700">
        {currentCodespace?.files?.map(file => (
          <div
            key={file._id}
            className={`px-4 py-2 border-r border-gray-700 flex items-center cursor-pointer ${
              activeFileId === file._id ? 'bg-gray-800 text-white' : 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
            }`}
            onClick={() => {
              setActiveFileId(file._id);
              setCode(file.content);
            }}
          >
            <span className="font-medium">{file.name}</span>
            {currentCodespace.files.length > 1 && (
              <button
                className="ml-2 text-gray-400 hover:text-red-400"
                onClick={(e) => handleDeleteFile(file._id, e)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        ))}

        {/* If no codespace is loaded, show a default tab */}
        {!currentCodespace && (
          <div className="px-4 py-2 bg-gray-800 border-r border-gray-700 flex items-center text-white">
            <span className="font-medium">main.js</span>
          </div>
        )}

        <button
          className="px-3 py-2 text-gray-400 hover:text-green-400 flex items-center"
          onClick={handleAddFile}
          title="Add new file"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
      </div>

      {/* Main content and sidebar */}
      <div className="flex-grow flex">
        {/* Code editor */}
        <div className="flex-grow flex flex-col">
          <div className="flex-grow border border-gray-700 bg-gray-800/70 backdrop-blur-sm code-editor">
            <textarea
              ref={editorRef}
              className="w-full h-full p-4 font-mono text-sm bg-transparent text-gray-200 focus:outline-none resize-none"
              placeholder="// Write your code here"
              value={code}
              onChange={handleCodeChange}
              spellCheck="false"
            ></textarea>
          </div>

          {/* Output panel */}
          <div className="h-48 mt-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium text-gray-300">Output</h3>
              <div className="flex space-x-2">
                <button
                  className="text-xs px-2 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors duration-200"
                  onClick={() => setOutput('')}
                >
                  Clear
                </button>
                <button
                  className="text-xs px-2 py-1 bg-green-600 hover:bg-green-700 text-white rounded transition-colors duration-200 flex items-center"
                  onClick={handleRunCode}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                  </svg>
                  Run
                </button>
              </div>
            </div>
            <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm h-full overflow-y-auto border border-gray-700">
              {output ? (
                <pre className="whitespace-pre-wrap">{output}</pre>
              ) : (
                <p className="text-gray-500">// Output will appear here after running your code</p>
              )}
            </div>
          </div>

          {/* Terminal */}
          <Terminal
            isOpen={isTerminalOpen}
            onClose={() => setIsTerminalOpen(false)}
            initialOutput={output}
          />
        </div>

        {/* Right sidebar */}
        <EditorSidebar
          onRunCode={handleRunCode}
          onSaveFile={handleSaveFile}
          onShare={handleShare}
          shareUrl={shareUrl}
          showShareTooltip={showShareTooltip}
          activeFile={activeFile}
          onLanguageChange={handleLanguageChange}
          isPublic={currentCodespace?.isPublic || false}
        />
      </div>
    </div>
  )
}

export default CodeEditor