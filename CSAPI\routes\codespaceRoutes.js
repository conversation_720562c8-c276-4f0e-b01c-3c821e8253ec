const express = require('express');
const router = express.Router();
const {
  createCodespace,
  getMyCodespaces,
  getCodespace,
  updateCodespace,
  deleteCodespace,
  updateFile,
  addFile,
  deleteFile,
  getCodespaceByAccessCode
} = require('../controllers/codespaceController');
const { protect } = require('../middleware/authMiddleware');

// Public routes
router.get('/access/:code', getCodespaceByAccessCode);

// All other routes are protected
router.use(protect);

// Codespace routes
router.post('/', createCodespace);
router.get('/', getMyCodespaces);
router.get('/:id', getCodespace);
router.put('/:id', updateCodespace);
router.delete('/:id', deleteCodespace);

// File operations
router.put('/:id/files/:fileId', updateFile);
router.post('/:id/files', addFile);
router.delete('/:id/files/:fileId', deleteFile);

module.exports = router;
