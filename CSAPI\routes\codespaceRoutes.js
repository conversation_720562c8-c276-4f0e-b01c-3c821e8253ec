const express = require('express');
const router = express.Router();
const {
  createCodespace,
  getMyCodespaces,
  getCodespace,
  updateCodespace,
  deleteCodespace
} = require('../controllers/codespaceController');
const { protect } = require('../middleware/authMiddleware');

// All routes are protected
router.use(protect);

// Codespace routes
router.post('/', createCodespace);
router.get('/', getMyCodespaces);
router.get('/:id', getCodespace);
router.put('/:id', updateCodespace);
router.delete('/:id', deleteCodespace);

module.exports = router;
