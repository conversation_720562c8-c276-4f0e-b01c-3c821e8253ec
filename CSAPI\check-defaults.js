// Simple script to check if default settings are in database
const mongoose = require('mongoose');

async function checkDefaults() {
  try {
    console.log('🔍 Checking default settings in database...\n');
    
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/codespace');
    console.log('✅ Connected to MongoDB');
    
    // Load the DefaultSettings model
    const DefaultSettings = require('./models/DefaultSettings');
    
    // Check if default settings exist
    const defaultCount = await DefaultSettings.countDocuments();
    console.log(`📊 Found ${defaultCount} default setting categories`);
    
    if (defaultCount === 0) {
      console.log('⚠️  No default settings found. Initializing...');
      await DefaultSettings.initializeDefaults();
      console.log('✅ Default settings initialized');
    }
    
    // Get all defaults
    const defaults = await DefaultSettings.getAllDefaults();
    console.log('\n📋 Default settings categories:');
    
    Object.keys(defaults).forEach(category => {
      const settingsCount = Object.keys(defaults[category] || {}).length;
      console.log(`   ${category}: ${settingsCount} settings`);
    });
    
    // Show some sample editor settings
    if (defaults.editor) {
      console.log('\n🎨 Sample editor defaults:');
      console.log(`   fontSize: ${defaults.editor.fontSize}`);
      console.log(`   fontFamily: ${defaults.editor.fontFamily}`);
      console.log(`   theme: ${defaults.editor.theme}`);
      console.log(`   wordWrap: ${defaults.editor.wordWrap}`);
      console.log(`   lineNumbers: ${defaults.editor.lineNumbers}`);
    }
    
    // Show UI defaults
    if (defaults.ui) {
      console.log('\n🖥️  UI defaults:');
      console.log(`   showFooter: ${defaults.ui.showFooter}`);
      console.log(`   allowMultiplePanels: ${defaults.ui.allowMultiplePanels}`);
      console.log(`   primaryPanelToggleBehavior: ${defaults.ui.primaryPanelToggleBehavior}`);
    }
    
    console.log('\n🎉 Default settings check completed successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the check
checkDefaults();
