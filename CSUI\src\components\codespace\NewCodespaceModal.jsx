import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import useCodespaceStore from '../../store/codespaceStore';

const NewCodespaceModal = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { createCodespace, isLoading, error } = useCodespaceStore();

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isPublic, setIsPublic] = useState(false);
  const [language, setLanguage] = useState('javascript');

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Create initial file based on selected language
      let initialContent = '';

      switch (language) {
        case 'javascript':
          initialContent = `// ${name}\n// Created: ${new Date().toLocaleDateString()}\n\nconsole.log('Hello, CodeSpace!');`;
          break;
        case 'python':
          initialContent = `# ${name}\n# Created: ${new Date().toLocaleDateString()}\n\nprint('Hello, CodeSpace!')`;
          break;
        case 'html':
          initialContent = `<!DOCTYPE html>\n<html lang="en">\n<head>\n  <meta charset="UTF-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1.0">\n  <title>${name}</title>\n</head>\n<body>\n  <h1>Hello, CodeSpace!</h1>\n</body>\n</html>`;
          break;
        default:
          initialContent = `// ${name}\n// Created: ${new Date().toLocaleDateString()}\n\n// Start coding here`;
      }

      const files = [{
        name: `main.${language === 'html' ? 'html' : language === 'python' ? 'py' : 'js'}`,
        language,
        content: initialContent
      }];

      const response = await createCodespace({
        name,
        description,
        isPublic,
        files
      });

      // Navigate to the new codespace
      navigate(`/codespace/${response.codespace._id}`);
      onClose();
    } catch (err) {
      console.error('Error creating codespace:', err);
      // Error is handled by the store and will be available in the error state
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-modal">
      <div className="bg-gray-800 rounded-lg shadow-xl w-full max-w-md p-6 border border-gray-700">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">Create New Codespace</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-md text-red-200 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
              Name <span className="text-red-400">*</span>
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-1 focus:ring-green-500"
              placeholder="My Awesome Project"
              required
            />
          </div>

          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-1">
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-1 focus:ring-green-500 resize-none h-20"
              placeholder="A brief description of your project"
            />
          </div>

          <div className="mb-4">
            <label htmlFor="language" className="block text-sm font-medium text-gray-300 mb-1">
              Primary Language
            </label>
            <select
              id="language"
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-1 focus:ring-green-500"
            >
              <option value="javascript">JavaScript</option>
              <option value="python">Python</option>
              <option value="html">HTML</option>
              <option value="css">CSS</option>
              <option value="java">Java</option>
              <option value="cpp">C++</option>
            </select>
          </div>

          <div className="mb-6">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPublic"
                checked={isPublic}
                onChange={(e) => setIsPublic(e.target.checked)}
                className="h-4 w-4 text-green-500 focus:ring-green-500 border-gray-600 rounded bg-gray-700"
              />
              <label htmlFor="isPublic" className="ml-2 block text-sm text-gray-300">
                Make this codespace public
              </label>
            </div>
            <p className="text-xs text-gray-400 mt-1 ml-6">
              Public codespaces can be accessed by anyone with the link
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating...
                </>
              ) : (
                'Create Codespace'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewCodespaceModal;
