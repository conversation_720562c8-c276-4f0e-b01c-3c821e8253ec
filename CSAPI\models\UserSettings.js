const mongoose = require('mongoose');

// Default settings that will be assigned to new users
const getDefaultEditorSettings = () => ({
  // Basic Editor Settings
  fontSize: 14,
  fontFamily: "'Fira Code', 'Cascadia Code', 'JetBrains Mono', 'SF Mono', Monaco, Menlo, 'Ubuntu Mono', monospace",
  fontWeight: 'normal',
  lineHeight: 1.5,
  letterSpacing: 0,

  // Theme and Appearance
  theme: 'vs-dark',
  colorTheme: 'Default Dark+',

  // Editor Behavior
  tabSize: 2,
  insertSpaces: true,
  detectIndentation: true,
  trimAutoWhitespace: true,
  wordWrap: 'on',
  wordWrapColumn: 80,
  wrappingIndent: 'indent',

  // Line Numbers and Rulers
  lineNumbers: 'on',
  lineNumbersMinChars: 3,
  rulers: [],

  // Cursor and Selection
  cursorStyle: 'line',
  cursorWidth: 2,
  cursorBlinking: 'blink',
  selectionHighlight: true,
  occurrencesHighlight: true,

  // Scrolling
  scrollBeyondLastLine: true,
  scrollBeyondLastColumn: 5,
  smoothScrolling: true,
  mouseWheelScrollSensitivity: 1,

  // Minimap
  minimapEnabled: true,
  minimapSide: 'right',
  minimapSize: 'proportional',
  minimapShowSlider: 'mouseover',

  // Code Folding
  folding: true,
  foldingStrategy: 'auto',
  showFoldingControls: 'mouseover',

  // Suggestions and IntelliSense
  quickSuggestions: true,
  suggestOnTriggerCharacters: true,
  acceptSuggestionOnEnter: 'on',
  tabCompletion: 'off',

  // Formatting
  formatOnType: true,
  formatOnPaste: true,
  autoIndent: 'advanced',

  // Bracket Matching
  matchBrackets: 'always',
  bracketPairColorization: true,

  // Comments and Strings
  autoClosingBrackets: 'languageDefined',
  autoClosingQuotes: 'languageDefined',
  autoSurround: 'languageDefined',

  // Find and Replace
  find: {
    seedSearchStringFromSelection: true,
    autoFindInSelection: 'never'
  },

  // Multi-cursor
  multiCursorModifier: 'alt',
  multiCursorMergeOverlapping: true,

  // Accessibility
  accessibilitySupport: 'auto',

  // Performance
  renderWhitespace: 'selection',
  renderControlCharacters: false,
  renderIndentGuides: true,
  highlightActiveIndentGuide: true,

  // Advanced
  useTabStops: true,
  emptySelectionClipboard: true,
  copyWithSyntaxHighlighting: true,
  links: true,
  contextmenu: true,
  mouseWheelZoom: false,
  dragAndDrop: true,

  // Additional Editor Features
  wordBasedSuggestions: true,
  wordBasedSuggestionsOnlySameLanguage: false,
  semanticHighlighting: true,
  occurrencesHighlight: true,
  codeLens: true,
  colorDecorators: true,
  lightbulb: {
    enabled: true
  },

  // Hover and Tooltips
  hover: {
    enabled: true,
    delay: 300,
    sticky: true
  },

  // Parameter Hints
  parameterHints: {
    enabled: true,
    cycle: false
  },

  // Code Actions
  codeActionsOnSave: {},
  codeActionsOnSaveTimeout: 750,

  // Diff Editor
  ignoreTrimWhitespace: true,
  renderSideBySide: true,

  // Performance Settings
  stopRenderingLineAfter: 10000,
  disableLayerHinting: false,
  disableMonospaceOptimizations: false,

  // Experimental Features
  experimentalWhitespaceRendering: 'svg',

  // Language-specific settings
  javascript: {
    validate: true,
    format: true,
    suggest: {
      autoImports: true,
      completeFunctionCalls: true
    }
  },
  typescript: {
    validate: true,
    format: true,
    suggest: {
      autoImports: true,
      completeFunctionCalls: true
    }
  },
  html: {
    format: true,
    autoClosingTags: true,
    suggest: {
      html5: true
    }
  },
  css: {
    validate: true,
    format: true,
    lint: {
      compatibleVendorPrefixes: 'ignore',
      vendorPrefix: 'warning'
    }
  },
  json: {
    validate: true,
    format: true,
    schemas: []
  },

  // Additional Language Support
  markdown: {
    preview: {
      breaks: false,
      linkify: true,
      typographer: false
    }
  },

  // Terminal Integration
  terminal: {
    integrated: {
      fontSize: 14,
      fontFamily: 'monospace',
      cursorBlinking: true,
      cursorStyle: 'block'
    }
  }
});

const getDefaultUISettings = () => ({
  showFooter: false,
  allowMultiplePanels: true,
  primaryPanelToggleBehavior: 'shrink', // 'shrink' or 'minimize'
  secondaryPanelToggleBehavior: 'shrink', // 'shrink' or 'minimize'
  theme: 'dark',
  accentColor: 'green'
});

const getDefaultNotificationSettings = () => ({
  email: true,
  push: false,
  desktop: true,
  sound: true,
  settingsChanges: true,
  profileUpdates: false,
  systemUpdates: true
});

const getDefaultGeneralSettings = () => ({
  language: 'en',
  timezone: 'auto',
  dateFormat: 'MM/DD/YYYY',
  timeFormat: '12h',
  autoSave: true,
  autoSaveInterval: 30000 // 30 seconds
});

const userSettingsSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
    index: true
  },

  editorSettings: {
    type: Object,
    default: getDefaultEditorSettings
  },

  uiSettings: {
    type: Object,
    default: getDefaultUISettings
  },

  notificationSettings: {
    type: Object,
    default: getDefaultNotificationSettings
  },

  generalSettings: {
    type: Object,
    default: getDefaultGeneralSettings
  }
}, {
  timestamps: true // Automatically adds createdAt and updatedAt
});

// Static methods to get default settings
userSettingsSchema.statics.getDefaultEditorSettings = getDefaultEditorSettings;
userSettingsSchema.statics.getDefaultUISettings = getDefaultUISettings;
userSettingsSchema.statics.getDefaultNotificationSettings = getDefaultNotificationSettings;
userSettingsSchema.statics.getDefaultGeneralSettings = getDefaultGeneralSettings;

// Get all defaults in one object
userSettingsSchema.statics.getDefaults = function() {
  return {
    editor: getDefaultEditorSettings(),
    ui: getDefaultUISettings(),
    notifications: getDefaultNotificationSettings(),
    general: getDefaultGeneralSettings()
  };
};

// Instance method to reset specific category to defaults
userSettingsSchema.methods.resetCategory = function(category) {
  switch (category) {
    case 'editor':
      this.editorSettings = getDefaultEditorSettings();
      break;
    case 'ui':
      this.uiSettings = getDefaultUISettings();
      break;
    case 'notifications':
      this.notificationSettings = getDefaultNotificationSettings();
      break;
    case 'general':
      this.generalSettings = getDefaultGeneralSettings();
      break;
    default:
      throw new Error('Invalid category. Use: editor, ui, notifications, or general');
  }
  return this;
};

// Instance method to reset all settings to defaults
userSettingsSchema.methods.resetAllSettings = function() {
  this.editorSettings = getDefaultEditorSettings();
  this.uiSettings = getDefaultUISettings();
  this.notificationSettings = getDefaultNotificationSettings();
  this.generalSettings = getDefaultGeneralSettings();
  return this;
};

// Pre-save middleware to ensure defaults are set
userSettingsSchema.pre('save', function(next) {
  if (this.isNew) {
    // Ensure all settings have defaults for new documents
    if (!this.editorSettings || Object.keys(this.editorSettings).length === 0) {
      this.editorSettings = getDefaultEditorSettings();
    }
    if (!this.uiSettings || Object.keys(this.uiSettings).length === 0) {
      this.uiSettings = getDefaultUISettings();
    }
    if (!this.notificationSettings || Object.keys(this.notificationSettings).length === 0) {
      this.notificationSettings = getDefaultNotificationSettings();
    }
    if (!this.generalSettings || Object.keys(this.generalSettings).length === 0) {
      this.generalSettings = getDefaultGeneralSettings();
    }
  }
  next();
});

module.exports = mongoose.model('UserSettings', userSettingsSchema);
