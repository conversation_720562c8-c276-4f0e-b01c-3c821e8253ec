const mongoose = require('mongoose');

// Default Editor Settings Schema (matching your frontend)
const defaultEditorSettings = {
  // Basic Settings
  fontSize: { type: Number, default: 14 },
  fontFamily: { type: String, default: "'Fira Code', 'Cascadia Code', 'JetBrains Mono', 'SF Mono', Monaco, Menlo, 'Ubuntu Mono', monospace" },
  fontWeight: { type: String, default: 'normal' },
  lineHeight: { type: Number, default: 1.5 },
  letterSpacing: { type: Number, default: 0 },
  
  // Theme and Appearance
  theme: { type: String, default: 'vs-dark' },
  colorTheme: { type: String, default: 'codespace-dark' },
  
  // Line Numbers
  lineNumbers: { type: String, default: 'on' },
  lineNumbersMinChars: { type: Number, default: 3 },
  
  // Indentation
  tabSize: { type: Number, default: 2 },
  insertSpaces: { type: Boolean, default: true },
  detectIndentation: { type: Boolean, default: true },
  trimAutoWhitespace: { type: Boolean, default: true },
  
  // Word Wrapping
  wordWrap: { type: String, default: 'on' },
  wordWrapColumn: { type: Number, default: 80 },
  wordWrapMinified: { type: Boolean, default: true },
  wrappingIndent: { type: String, default: 'indent' },
  
  // Scrolling
  scrollBeyondLastLine: { type: Boolean, default: false },
  scrollBeyondLastColumn: { type: Number, default: 5 },
  smoothScrolling: { type: Boolean, default: true },
  mouseWheelScrollSensitivity: { type: Number, default: 1 },
  fastScrollSensitivity: { type: Number, default: 5 },
  
  // Cursor
  cursorStyle: { type: String, default: 'line' },
  cursorWidth: { type: Number, default: 2 },
  cursorBlinking: { type: String, default: 'smooth' },
  cursorSmoothCaretAnimation: { type: Boolean, default: true },
  
  // Selection
  selectOnLineNumbers: { type: Boolean, default: true },
  selectionHighlight: { type: Boolean, default: true },
  occurrencesHighlight: { type: Boolean, default: true },
  renderLineHighlight: { type: String, default: 'all' },
  renderLineHighlightOnlyWhenFocus: { type: Boolean, default: false },
  
  // Minimap
  minimapEnabled: { type: Boolean, default: true },
  minimapSide: { type: String, default: 'right' },
  minimapSize: { type: String, default: 'proportional' },
  minimapShowSlider: { type: String, default: 'mouseover' },
  minimapRenderCharacters: { type: Boolean, default: true },
  minimapMaxColumn: { type: Number, default: 120 },
  
  // Code Folding
  folding: { type: Boolean, default: true },
  foldingStrategy: { type: String, default: 'auto' },
  foldingHighlight: { type: Boolean, default: true },
  unfoldOnClickAfterEndOfLine: { type: Boolean, default: false },
  
  // Bracket Matching
  matchBrackets: { type: String, default: 'always' },
  bracketPairColorization: { type: Boolean, default: true },
  
  // Guides
  guides: {
    bracketPairs: { type: Boolean, default: true },
    bracketPairsHorizontal: { type: Boolean, default: true },
    highlightActiveBracketPair: { type: Boolean, default: true },
    indentation: { type: Boolean, default: true },
    highlightActiveIndentation: { type: Boolean, default: true }
  },
  
  // IntelliSense
  quickSuggestions: { type: Boolean, default: true },
  quickSuggestionsDelay: { type: Number, default: 10 },
  suggestOnTriggerCharacters: { type: Boolean, default: true },
  acceptSuggestionOnEnter: { type: String, default: 'on' },
  acceptSuggestionOnCommitCharacter: { type: Boolean, default: true },
  snippetSuggestions: { type: String, default: 'top' },
  emptySelectionClipboard: { type: Boolean, default: true },
  wordBasedSuggestions: { type: Boolean, default: true },
  suggestSelection: { type: String, default: 'recentlyUsed' },
  suggestFontSize: { type: Number, default: 0 },
  suggestLineHeight: { type: Number, default: 0 },
  
  // Auto-completion and Formatting
  autoIndent: { type: String, default: 'full' },
  formatOnType: { type: Boolean, default: true },
  formatOnPaste: { type: Boolean, default: true },
  autoClosingBrackets: { type: String, default: 'always' },
  autoClosingQuotes: { type: String, default: 'always' },
  autoSurround: { type: String, default: 'languageDefined' },
  
  // Find and Replace
  find: {
    seedSearchStringFromSelection: { type: String, default: 'always' },
    autoFindInSelection: { type: String, default: 'never' },
    addExtraSpaceOnTop: { type: Boolean, default: true },
    loop: { type: Boolean, default: true }
  },
  
  // Whitespace and Rendering
  renderWhitespace: { type: String, default: 'selection' },
  renderControlCharacters: { type: Boolean, default: false },
  renderIndentGuides: { type: Boolean, default: true },
  highlightActiveIndentGuide: { type: Boolean, default: true },
  renderValidationDecorations: { type: String, default: 'editable' },
  
  // Performance
  stopRenderingLineAfter: { type: Number, default: 10000 },
  disableLayerHinting: { type: Boolean, default: false },
  disableMonospaceOptimizations: { type: Boolean, default: false },
  
  // Accessibility
  accessibilitySupport: { type: String, default: 'auto' },
  accessibilityPageSize: { type: Number, default: 10 },
  
  // Advanced
  contextmenu: { type: Boolean, default: true },
  mouseStyle: { type: String, default: 'text' },
  multiCursorModifier: { type: String, default: 'alt' },
  multiCursorMergeOverlapping: { type: Boolean, default: true },
  multiCursorPaste: { type: String, default: 'spread' },
  columnSelection: { type: Boolean, default: false },
  copyWithSyntaxHighlighting: { type: Boolean, default: true },
  useTabStops: { type: Boolean, default: true },
  
  // Editor Layout
  automaticLayout: { type: Boolean, default: true },
  glyphMargin: { type: Boolean, default: true },
  lineDecorationsWidth: { type: Number, default: 10 },
  lineNumbersWidth: { type: Number, default: 50 },
  overviewRulerBorder: { type: Boolean, default: true },
  overviewRulerLanes: { type: Number, default: 3 },
  hideCursorInOverviewRuler: { type: Boolean, default: false },
  
  // Hover
  hover: {
    enabled: { type: Boolean, default: true },
    delay: { type: Number, default: 300 },
    sticky: { type: Boolean, default: true }
  },
  
  // Parameter Hints
  parameterHints: {
    enabled: { type: Boolean, default: true },
    cycle: { type: Boolean, default: false }
  },
  
  // Code Lens
  codeLens: { type: Boolean, default: true },
  codeLensFontFamily: { type: String, default: '' },
  codeLensFontSize: { type: Number, default: 12 },
  
  // Links
  links: { type: Boolean, default: true },
  
  // Comments
  comments: {
    insertSpace: { type: Boolean, default: true },
    ignoreEmptyLines: { type: Boolean, default: true }
  }
};

// Default UI Settings Schema
const defaultUISettings = {
  showFooter: { type: Boolean, default: false },
  allowMultiplePanels: { type: Boolean, default: true },
  primaryPanelToggleBehavior: { type: String, default: 'shrink' },
  secondaryPanelToggleBehavior: { type: String, default: 'shrink' },
  isPrimaryPanelCollapsed: { type: Boolean, default: false },
  isSecondaryPanelCollapsed: { type: Boolean, default: false }
};

// Default Notification Settings
const defaultNotificationSettings = {
  email: { type: Boolean, default: true },
  push: { type: Boolean, default: false },
  desktop: { type: Boolean, default: true },
  sound: { type: Boolean, default: true },
  codespaceInvites: { type: Boolean, default: true },
  collaborationUpdates: { type: Boolean, default: true },
  systemUpdates: { type: Boolean, default: true }
};

// Default General Settings
const defaultGeneralSettings = {
  language: { type: String, default: 'en' },
  timezone: { type: String, default: 'auto' },
  dateFormat: { type: String, default: 'MM/DD/YYYY' },
  timeFormat: { type: String, default: '12h' },
  autoSave: { type: Boolean, default: true },
  autoSaveInterval: { type: Number, default: 30 } // seconds
};

// Main UserSettings Schema
const userSettingsSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  
  // Settings Categories
  editorSettings: {
    type: defaultEditorSettings,
    default: () => ({})
  },
  
  uiSettings: {
    type: defaultUISettings,
    default: () => ({})
  },
  
  notificationSettings: {
    type: defaultNotificationSettings,
    default: () => ({})
  },
  
  generalSettings: {
    type: defaultGeneralSettings,
    default: () => ({})
  },
  
  // Metadata
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Update lastUpdated on save
userSettingsSchema.pre('save', function(next) {
  this.lastUpdated = new Date();
  next();
});

// Index for efficient queries
userSettingsSchema.index({ userId: 1 });

// Static method to get default settings
userSettingsSchema.statics.getDefaults = function() {
  return {
    editorSettings: this.getDefaultEditorSettings(),
    uiSettings: this.getDefaultUISettings(),
    notificationSettings: this.getDefaultNotificationSettings(),
    generalSettings: this.getDefaultGeneralSettings()
  };
};

// Static methods for individual default settings
userSettingsSchema.statics.getDefaultEditorSettings = function() {
  const defaults = {};
  Object.keys(defaultEditorSettings).forEach(key => {
    if (defaultEditorSettings[key].default !== undefined) {
      defaults[key] = defaultEditorSettings[key].default;
    }
  });
  return defaults;
};

userSettingsSchema.statics.getDefaultUISettings = function() {
  const defaults = {};
  Object.keys(defaultUISettings).forEach(key => {
    defaults[key] = defaultUISettings[key].default;
  });
  return defaults;
};

userSettingsSchema.statics.getDefaultNotificationSettings = function() {
  const defaults = {};
  Object.keys(defaultNotificationSettings).forEach(key => {
    defaults[key] = defaultNotificationSettings[key].default;
  });
  return defaults;
};

userSettingsSchema.statics.getDefaultGeneralSettings = function() {
  const defaults = {};
  Object.keys(defaultGeneralSettings).forEach(key => {
    defaults[key] = defaultGeneralSettings[key].default;
  });
  return defaults;
};

module.exports = mongoose.model('UserSettings', userSettingsSchema);
