const os = require('os');

/**
 * Display network information for the server
 */
function showNetworkInfo() {
  try {
    const networkInterfaces = os.networkInterfaces();
    const hostname = os.hostname();
    
    console.log('\n🌐 Network Information:');
    console.log(`📍 Hostname: ${hostname}`);
    
    // Get all network interfaces
    Object.keys(networkInterfaces).forEach(interfaceName => {
      const interfaces = networkInterfaces[interfaceName];
      
      interfaces.forEach(iface => {
        // Skip internal/loopback interfaces and IPv6 for simplicity
        if (iface.family === 'IPv4' && !iface.internal) {
          console.log(`🔗 ${interfaceName}: ${iface.address}`);
          
          // Show potential access URLs
          const port = process.env.PORT || 3000;
          console.log(`   📡 API Access: http://${iface.address}:${port}`);
          
          // If it's a local network IP, show custom domain format
          if (iface.address.startsWith('192.168.') || 
              iface.address.startsWith('10.') || 
              iface.address.startsWith('172.')) {
            console.log(`   🏠 Custom Domain: http://codespace.roy:${port}`);
          }
        }
      });
    });
    
    console.log(''); // Empty line for spacing
    
  } catch (error) {
    console.warn('⚠️  Could not retrieve network information:', error.message);
  }
}

module.exports = showNetworkInfo;
