const os = require('os');

const getNetworkInterfaces = () => {
  const interfaces = os.networkInterfaces();
  const addresses = [];

  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        addresses.push({
          name: name,
          address: interface.address
        });
      }
    }
  }

  return addresses;
};

const showNetworkInfo = () => {
  const PORT = process.env.PORT || 3000;
  const addresses = getNetworkInterfaces();

  console.log('\n🌐 API Server Network Information:');
  console.log('=====================================');
  console.log(`📍 Local:    http://localhost:${PORT}`);
  
  if (addresses.length > 0) {
    console.log('📡 Network:');
    addresses.forEach(addr => {
      console.log(`   ${addr.name}: http://${addr.address}:${PORT}`);
    });
  } else {
    console.log('❌ No network interfaces found');
  }
  
  console.log('\n💡 Tips:');
  console.log('- Use Local URL for same computer access');
  console.log('- Use Network URL for LAN device access');
  console.log('- Make sure firewall allows port 3000');
  console.log('=====================================\n');
};

module.exports = showNetworkInfo;

// Run if called directly
if (require.main === module) {
  showNetworkInfo();
}
