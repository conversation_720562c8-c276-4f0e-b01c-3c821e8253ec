// Load environment variables from .env file FIRST
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Debug: Check if environment variables are loaded
console.log('Environment check:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('MONGODB_URI:', process.env.MONGODB_URI ? 'LOADED' : 'NOT LOADED');
console.log('GOOGLE_CLIENT_ID:', process.env.GOOGLE_CLIENT_ID ? 'LOADED' : 'NOT LOADED');

const express = require('express');
const connectDB = require('./config/db');
const passport = require('passport');
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const settingsRoutes = require('./routes/settingsRoutes');
const errorHandler = require('./middleware/errorMiddleware');
const corsMiddleware = require('./middleware/corsMiddleware');
const requestSizeLogger = require('./middleware/requestSizeLogger');

// Connect to MongoDB
connectDB();

// Initialize passport
require('./config/passport');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(requestSizeLogger); // Log request sizes
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(corsMiddleware);
app.use(passport.initialize());

// Welcome route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to CodeSpace API' });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/user/settings', settingsRoutes);

// Error handling middleware
app.use(errorHandler);

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Server running on port ${PORT} in ${process.env.NODE_ENV} mode`);

  // Show network information
  const showNetworkInfo = require('./scripts/show-network-info');
  showNetworkInfo();

  // Log email configuration status
  if (process.env.EMAIL_SERVICE && process.env.EMAIL_USERNAME && process.env.EMAIL_FROM) {
    console.log(`📧 Email service configured: ${process.env.EMAIL_SERVICE}`);
    console.log(`📤 Sending from: ${process.env.EMAIL_FROM}`);
  } else {
    console.warn('⚠️  Email service not fully configured. Check your .env file.');
  }
});
