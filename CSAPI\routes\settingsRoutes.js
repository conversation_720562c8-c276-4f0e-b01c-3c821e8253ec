const express = require('express');
const router = express.Router();
const {
  getUserSettings,
  updateUserSettings,
  updateEditorSettings,
  updateUISettings,
  updateNotificationSettings,
  updateGeneralSettings,
  resetUserSettings,
  getSettingsSchema,
  getFormattedSettings,
  syncSettings,
  exportSettings,
  importSettings
} = require('../controllers/settingsController');
const { protect } = require('../middleware/authMiddleware');

// Apply authentication middleware to all routes
router.use(protect);

// @route   GET /api/user/settings
// @desc    Get all user settings
// @access  Private
router.get('/', getUserSettings);

// @route   PUT /api/user/settings
// @desc    Update all user settings
// @access  Private
router.put('/', updateUserSettings);

// @route   PATCH /api/user/settings/editor
// @desc    Update editor settings only
// @access  Private
router.patch('/editor', updateEditorSettings);

// @route   PATCH /api/user/settings/ui
// @desc    Update UI settings only
// @access  Private
router.patch('/ui', updateUISettings);

// @route   PATCH /api/user/settings/notifications
// @desc    Update notification settings only
// @access  Private
router.patch('/notifications', updateNotificationSettings);

// @route   PATCH /api/user/settings/general
// @desc    Update general settings only
// @access  Private
router.patch('/general', updateGeneralSettings);

// @route   POST /api/user/settings/reset
// @desc    Reset user settings to defaults
// @access  Private
router.post('/reset', resetUserSettings);

// @route   GET /api/user/settings/schema
// @desc    Get settings schema/defaults
// @access  Private
router.get('/schema', getSettingsSchema);

// @route   GET /api/user/settings/formatted
// @desc    Get formatted settings for frontend
// @access  Private
router.get('/formatted', getFormattedSettings);

// @route   POST /api/user/settings/sync
// @desc    Bulk update settings (for syncing from frontend)
// @access  Private
router.post('/sync', syncSettings);

// @route   GET /api/user/settings/export
// @desc    Export user settings
// @access  Private
router.get('/export', exportSettings);

// @route   POST /api/user/settings/import
// @desc    Import user settings
// @access  Private
router.post('/import', importSettings);

module.exports = router;
