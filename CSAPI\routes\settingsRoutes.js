const express = require('express');
const router = express.Router();
const {
  getUserSettings,
  updateUserSettings,
  updateSettingsCategory,
  resetUserSettings,
  getSettingsSchema,
  exportUserSettings,
  importUserSettings
} = require('../controllers/settingsController');
const { protect } = require('../middleware/authMiddleware');

// All routes are protected
router.use(protect);

// Main settings routes
router.route('/')
  .get(getUserSettings)      // GET /api/user/settings
  .put(updateUserSettings);  // PUT /api/user/settings

// Category-specific routes
router.patch('/:category', updateSettingsCategory); // PATCH /api/user/settings/:category

// Utility routes
router.post('/reset', resetUserSettings);           // POST /api/user/settings/reset
router.get('/schema', getSettingsSchema);           // GET /api/user/settings/schema
router.get('/export', exportUserSettings);         // GET /api/user/settings/export
router.post('/import', importUserSettings);        // POST /api/user/settings/import

module.exports = router;
