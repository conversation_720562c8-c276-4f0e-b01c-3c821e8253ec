import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import notificationService from '../services/notificationService'

const useNotificationStore = create(
  persist(
    (set, get) => ({
      // State
      notifications: [],
      unreadCount: 0,
      isLoading: false,
      error: null,
      lastFetch: null,
      
      // Toast notifications (temporary UI notifications)
      toasts: [],
      nextToastId: 1,

      // Actions
      /**
       * Fetch notifications from server
       */
      fetchNotifications: async (options = {}) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await notificationService.getNotifications(options)
          const formattedNotifications = response.notifications.map(
            notificationService.formatNotification
          )
          
          set({
            notifications: formattedNotifications,
            unreadCount: response.unreadCount,
            isLoading: false,
            lastFetch: new Date().toISOString()
          })
          
          return formattedNotifications
        } catch (error) {
          set({ 
            error: error.message, 
            isLoading: false 
          })
          throw error
        }
      },

      /**
       * Fetch unread count only
       */
      fetchUnreadCount: async () => {
        try {
          const count = await notificationService.getUnreadCount()
          set({ unreadCount: count })
          return count
        } catch (error) {
          console.error('Error fetching unread count:', error)
          return get().unreadCount
        }
      },

      /**
       * Mark notification as read
       */
      markAsRead: async (notificationId) => {
        try {
          await notificationService.markAsRead(notificationId)
          
          set(state => ({
            notifications: state.notifications.map(notification =>
              notification.id === notificationId
                ? { ...notification, isRead: true, readAt: new Date().toISOString() }
                : notification
            ),
            unreadCount: Math.max(0, state.unreadCount - 1)
          }))
          
          return true
        } catch (error) {
          set({ error: error.message })
          throw error
        }
      },

      /**
       * Mark all notifications as read
       */
      markAllAsRead: async () => {
        try {
          await notificationService.markAllAsRead()
          
          set(state => ({
            notifications: state.notifications.map(notification => ({
              ...notification,
              isRead: true,
              readAt: new Date().toISOString()
            })),
            unreadCount: 0
          }))
          
          return true
        } catch (error) {
          set({ error: error.message })
          throw error
        }
      },

      /**
       * Delete notification
       */
      deleteNotification: async (notificationId) => {
        try {
          await notificationService.deleteNotification(notificationId)
          
          set(state => {
            const notification = state.notifications.find(n => n.id === notificationId)
            const wasUnread = notification && !notification.isRead
            
            return {
              notifications: state.notifications.filter(n => n.id !== notificationId),
              unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount
            }
          })
          
          return true
        } catch (error) {
          set({ error: error.message })
          throw error
        }
      },

      /**
       * Add a new notification (for real-time updates)
       */
      addNotification: (notification) => {
        const formatted = notificationService.formatNotification(notification)
        
        set(state => ({
          notifications: [formatted, ...state.notifications],
          unreadCount: formatted.isRead ? state.unreadCount : state.unreadCount + 1
        }))
        
        // Show browser notification if enabled
        if (notificationService.isBrowserNotificationSupported()) {
          notificationService.showBrowserNotification(
            formatted.title,
            formatted.message,
            {
              tag: formatted.id,
              data: formatted.data
            }
          )
        }
      },

      /**
       * Clear all notifications
       */
      clearNotifications: () => {
        set({
          notifications: [],
          unreadCount: 0,
          error: null
        })
      },

      /**
       * Clear error
       */
      clearError: () => {
        set({ error: null })
      },

      // Toast notification methods
      /**
       * Add toast notification
       */
      addToast: (toast) => {
        const id = get().nextToastId
        const newToast = {
          id,
          type: 'info',
          duration: 5000,
          ...toast,
          createdAt: new Date().toISOString()
        }
        
        set(state => ({
          toasts: [...state.toasts, newToast],
          nextToastId: state.nextToastId + 1
        }))
        
        // Auto remove toast after duration
        if (newToast.duration > 0) {
          setTimeout(() => {
            get().removeToast(id)
          }, newToast.duration)
        }
        
        return id
      },

      /**
       * Remove toast notification
       */
      removeToast: (toastId) => {
        set(state => ({
          toasts: state.toasts.filter(toast => toast.id !== toastId)
        }))
      },

      /**
       * Clear all toasts
       */
      clearToasts: () => {
        set({ toasts: [] })
      },

      /**
       * Show success toast
       */
      showSuccess: (message, options = {}) => {
        return get().addToast({
          type: 'success',
          title: 'Success',
          message,
          ...options
        })
      },

      /**
       * Show error toast
       */
      showError: (message, options = {}) => {
        return get().addToast({
          type: 'error',
          title: 'Error',
          message,
          duration: 8000, // Longer duration for errors
          ...options
        })
      },

      /**
       * Show warning toast
       */
      showWarning: (message, options = {}) => {
        return get().addToast({
          type: 'warning',
          title: 'Warning',
          message,
          ...options
        })
      },

      /**
       * Show info toast
       */
      showInfo: (message, options = {}) => {
        return get().addToast({
          type: 'info',
          title: 'Info',
          message,
          ...options
        })
      },

      /**
       * Show settings saved toast
       */
      showSettingsSaved: (category = 'Settings') => {
        return get().showSuccess(`${category} saved successfully!`, {
          title: 'Settings Saved',
          duration: 3000
        })
      },

      /**
       * Show settings reset toast
       */
      showSettingsReset: (category = 'Settings') => {
        return get().showInfo(`${category} reset to defaults`, {
          title: 'Settings Reset',
          duration: 3000
        })
      },

      // Getters
      /**
       * Get notifications by category
       */
      getNotificationsByCategory: (category) => {
        return get().notifications.filter(n => n.category === category)
      },

      /**
       * Get unread notifications
       */
      getUnreadNotifications: () => {
        return get().notifications.filter(n => !n.isRead)
      },

      /**
       * Get recent notifications (last 24 hours)
       */
      getRecentNotifications: () => {
        return get().notifications.filter(n => n.isRecent)
      }
    }),
    {
      name: 'notification-store',
      partialize: (state) => ({
        // Only persist notifications and unread count
        notifications: state.notifications,
        unreadCount: state.unreadCount,
        lastFetch: state.lastFetch
      })
    }
  )
)

export default useNotificationStore
