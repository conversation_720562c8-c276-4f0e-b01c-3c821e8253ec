{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/less/less.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/less/less.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".less\",\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  identifierPlus: \"-?-?([a-zA-Z:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@nestedJSBegin\" },\n      [\"[ \\\\t\\\\r\\\\n]+\", \"\"],\n      { include: \"@comments\" },\n      { include: \"@keyword\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      [\"[*_]?[a-zA-Z\\\\-\\\\s]+(?=:.*(;|(\\\\\\\\$)))\", \"attribute.name\", \"@attribute\"],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"tag\", next: \"@urldeclaration\" }],\n      [\"[{}()\\\\[\\\\]]\", \"@brackets\"],\n      [\"[,:;]\", \"delimiter\"],\n      [\"#@identifierPlus\", \"tag.id\"],\n      [\"&\", \"tag\"],\n      [\"\\\\.@identifierPlus(?=\\\\()\", \"tag.class\", \"@attribute\"],\n      [\"\\\\.@identifierPlus\", \"tag.class\"],\n      [\"@identifierPlus\", \"tag\"],\n      { include: \"@operators\" },\n      [\"@(@identifier(?=[:,\\\\)]))\", \"variable\", \"@attribute\"],\n      [\"@(@identifier)\", \"variable\"],\n      [\"@\", \"key\", \"@atRules\"]\n    ],\n    nestedJSBegin: [\n      [\"``\", \"delimiter.backtick\"],\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@nestedJSEnd\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ]\n    ],\n    nestedJSEnd: [\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@pop\",\n          nextEmbedded: \"@pop\"\n        }\n      ]\n    ],\n    operators: [[\"[<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~]\", \"operator\"]],\n    keyword: [\n      [\n        \"(@[\\\\s]*import|![\\\\s]*important|true|false|when|iscolor|isnumber|isstring|iskeyword|isurl|ispixel|ispercentage|isem|hue|saturation|lightness|alpha|lighten|darken|saturate|desaturate|fadein|fadeout|fade|spin|mix|round|ceil|floor|percentage)\\\\b\",\n        \"keyword\"\n      ]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"tag\", next: \"@pop\" }]\n    ],\n    attribute: [\n      { include: \"@nestedJSBegin\" },\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      { include: \"@keyword\" },\n      [\"[a-zA-Z\\\\-]+(?=\\\\()\", \"attribute.value\", \"@attribute\"],\n      [\">\", \"operator\", \"@pop\"],\n      [\"@identifier\", \"attribute.value\"],\n      { include: \"@operators\" },\n      [\"@(@identifier)\", \"variable\"],\n      [\"[)\\\\}]\", \"@brackets\", \"@pop\"],\n      [\"[{}()\\\\[\\\\]>]\", \"@brackets\"],\n      [\"[;]\", \"delimiter\", \"@pop\"],\n      [\"[,=:]\", \"delimiter\"],\n      [\"\\\\s\", \"\"],\n      [\".\", \"attribute.value\"]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringsEndDoubleQuote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringsEndQuote\" }]\n    ],\n    stringsEndDoubleQuote: [\n      ['\\\\\\\\\"', \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    stringsEndQuote: [\n      [\"\\\\\\\\'\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    atRules: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      [\"[()]\", \"delimiter\"],\n      [\"[\\\\{;]\", \"delimiter\", \"@pop\"],\n      [\".\", \"key\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,cAAc,CAAC,MAAM,IAAI;AAAA,IACzB,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,8CAA8C;AAAA,MAChE,KAAK,IAAI,OAAO,sCAAsC;AAAA,IACxD;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACpD;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,iBAAiB;AAAA,MAC5B,CAAC,iBAAiB,EAAE;AAAA,MACpB,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,0CAA0C,kBAAkB,YAAY;AAAA,MACzE,CAAC,sBAAsB,EAAE,OAAO,OAAO,MAAM,kBAAkB,CAAC;AAAA,MAChE,CAAC,gBAAgB,WAAW;AAAA,MAC5B,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,oBAAoB,QAAQ;AAAA,MAC7B,CAAC,KAAK,KAAK;AAAA,MACX,CAAC,6BAA6B,aAAa,YAAY;AAAA,MACvD,CAAC,sBAAsB,WAAW;AAAA,MAClC,CAAC,mBAAmB,KAAK;AAAA,MACzB,EAAE,SAAS,aAAa;AAAA,MACxB,CAAC,6BAA6B,YAAY,YAAY;AAAA,MACtD,CAAC,kBAAkB,UAAU;AAAA,MAC7B,CAAC,KAAK,OAAO,UAAU;AAAA,IACzB;AAAA,IACA,eAAe;AAAA,MACb,CAAC,MAAM,oBAAoB;AAAA,MAC3B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW,CAAC,CAAC,8BAA8B,UAAU,CAAC;AAAA,IACtD,SAAS;AAAA,MACP;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,OAAO,EAAE,OAAO,OAAO,MAAM,OAAO,CAAC;AAAA,IACxC;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,uBAAuB,mBAAmB,YAAY;AAAA,MACvD,CAAC,KAAK,YAAY,MAAM;AAAA,MACxB,CAAC,eAAe,iBAAiB;AAAA,MACjC,EAAE,SAAS,aAAa;AAAA,MACxB,CAAC,kBAAkB,UAAU;AAAA,MAC7B,CAAC,UAAU,aAAa,MAAM;AAAA,MAC9B,CAAC,iBAAiB,WAAW;AAAA,MAC7B,CAAC,OAAO,aAAa,MAAM;AAAA,MAC3B,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,OAAO,EAAE;AAAA,MACV,CAAC,KAAK,iBAAiB;AAAA,IACzB;AAAA,IACA,UAAU;AAAA,MACR,CAAC,UAAU,WAAW,UAAU;AAAA,MAChC,CAAC,aAAa,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,WAAW,MAAM;AAAA,MAC5B,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,oCAAoC,EAAE,OAAO,0BAA0B,MAAM,SAAS,CAAC;AAAA,MACxF,CAAC,yBAAyB,qBAAqB;AAAA,IACjD;AAAA,IACA,OAAO;AAAA,MACL;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,yBAAyB,CAAC;AAAA,MACrE,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,mBAAmB,CAAC;AAAA,IACjE;AAAA,IACA,uBAAuB;AAAA,MACrB,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,UAAU,CAAC;AAAA,MACpD,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,iBAAiB;AAAA,MACf,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,UAAU,CAAC;AAAA,MACpD,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,QAAQ,WAAW;AAAA,MACpB,CAAC,UAAU,aAAa,MAAM;AAAA,MAC9B,CAAC,KAAK,KAAK;AAAA,IACb;AAAA,EACF;AACF;", "names": []}