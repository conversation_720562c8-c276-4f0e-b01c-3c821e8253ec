import * as api from './api';

/**
 * Create a new codespace
 * @param {Object} codespaceData - Codespace data
 * @returns {Promise} - Response data
 */
export const createCodespace = async (codespaceData) => {
  return await api.post('/codespaces', codespaceData, true);
};

/**
 * Get all codespaces for the current user
 * @returns {Promise} - Response data
 */
export const getMyCodespaces = async () => {
  return await api.get('/codespaces', true);
};

/**
 * Get a single codespace by ID
 * @param {string} id - Codespace ID
 * @returns {Promise} - Response data
 */
export const getCodespace = async (id) => {
  return await api.get(`/codespaces/${id}`, true);
};

/**
 * Get a codespace by access code (for public codespaces)
 * @param {string} accessCode - Codespace access code
 * @returns {Promise} - Response data
 */
export const getCodespaceByAccessCode = async (accessCode) => {
  return await api.get(`/codespaces/access/${accessCode}`, false);
};

/**
 * Get a codespace by slug (for public codespaces)
 * @param {string} slug - Codespace slug
 * @returns {Promise} - Response data
 */
export const getCodespaceBySlug = async (slug) => {
  return await api.get(`/codespaces/slug/${slug}`, false);
};

/**
 * Update a codespace
 * @param {string} id - Codespace ID
 * @param {Object} codespaceData - Updated codespace data
 * @returns {Promise} - Response data
 */
export const updateCodespace = async (id, codespaceData) => {
  return await api.put(`/codespaces/${id}`, codespaceData, true);
};

/**
 * Delete a codespace
 * @param {string} id - Codespace ID
 * @returns {Promise} - Response data
 */
export const deleteCodespace = async (id) => {
  return await api.del(`/codespaces/${id}`, true);
};

/**
 * Update a file in a codespace
 * @param {string} codespaceId - Codespace ID
 * @param {string} fileId - File ID
 * @param {Object} fileData - Updated file data
 * @returns {Promise} - Response data
 */
export const updateCodespaceFile = async (codespaceId, fileId, fileData) => {
  return await api.put(`/codespaces/${codespaceId}/files/${fileId}`, fileData, true);
};

/**
 * Add a new file to a codespace
 * @param {string} codespaceId - Codespace ID
 * @param {Object} fileData - File data
 * @returns {Promise} - Response data
 */
export const addCodespaceFile = async (codespaceId, fileData) => {
  return await api.post(`/codespaces/${codespaceId}/files`, fileData, true);
};

/**
 * Delete a file from a codespace
 * @param {string} codespaceId - Codespace ID
 * @param {string} fileId - File ID
 * @returns {Promise} - Response data
 */
export const deleteCodespaceFile = async (codespaceId, fileId) => {
  return await api.del(`/codespaces/${codespaceId}/files/${fileId}`, true);
};

/**
 * Add a collaborator to a codespace
 * @param {string} codespaceId - Codespace ID
 * @param {Object} collaboratorData - Collaborator data
 * @returns {Promise} - Response data
 */
export const addCollaborator = async (codespaceId, collaboratorData) => {
  return await api.post(`/codespaces/${codespaceId}/collaborators`, collaboratorData, true);
};

/**
 * Remove a collaborator from a codespace
 * @param {string} codespaceId - Codespace ID
 * @param {string} userId - User ID
 * @returns {Promise} - Response data
 */
export const removeCollaborator = async (codespaceId, userId) => {
  return await api.del(`/codespaces/${codespaceId}/collaborators/${userId}`, true);
};

/**
 * Update a collaborator's role in a codespace
 * @param {string} codespaceId - Codespace ID
 * @param {string} userId - User ID
 * @param {Object} roleData - Role data
 * @returns {Promise} - Response data
 */
export const updateCollaboratorRole = async (codespaceId, userId, roleData) => {
  return await api.put(`/codespaces/${codespaceId}/collaborators/${userId}`, roleData, true);
};
