const Codespace = require('../models/Codespace');
const User = require('../models/User');
const mongoose = require('mongoose');

/**
 * Create a new codespace
 * @route   POST /api/codespaces
 * @access  Private
 */
exports.createCodespace = async (req, res) => {
  try {
    const { name, description, isPublic, files } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a name for the codespace'
      });
    }

    // Create initial files if provided
    const initialFiles = files || [{
      name: 'main.js',
      language: 'javascript',
      content: '// Welcome to CodeSpace\n\nconsole.log("Hello, World!");'
    }];

    // Create new codespace
    const codespace = await Codespace.create({
      name,
      description: description || `Codespace created on ${new Date().toLocaleDateString()}`,
      owner: req.user.id,
      isPublic: isPublic || false,
      files: initialFiles
    });

    // Generate access code if public
    if (isPublic) {
      codespace.generateAccessCode();
      await codespace.save();
    }

    res.status(201).json({
      success: true,
      codespace,
      message: 'Codespace created successfully'
    });
  } catch (error) {
    console.error('Create codespace error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get all codespaces for the current user
 * @route   GET /api/codespaces
 * @access  Private
 */
exports.getMyCodespaces = async (req, res) => {
  try {
    // Find all codespaces owned by the user or where user is a collaborator
    const codespaces = await Codespace.find({
      $or: [
        { owner: req.user.id },
        { 'collaborators.user': req.user.id }
      ]
    }).sort({ lastAccessed: -1 });

    res.status(200).json({
      success: true,
      count: codespaces.length,
      codespaces
    });
  } catch (error) {
    console.error('Get codespaces error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Get a single codespace by ID
 * @route   GET /api/codespaces/:id
 * @access  Private/Public (depending on codespace settings)
 */
exports.getCodespace = async (req, res) => {
  try {
    const codespace = await Codespace.findById(req.params.id)
      .populate('owner', 'name email profilePicture')
      .populate('collaborators.user', 'name email profilePicture');

    if (!codespace) {
      return res.status(404).json({
        success: false,
        message: 'Codespace not found'
      });
    }

    // Check if user has access to this codespace
    const isOwner = codespace.owner._id.toString() === req.user.id;
    const isCollaborator = codespace.collaborators.some(
      collab => collab.user._id.toString() === req.user.id
    );
    const isPublic = codespace.isPublic;

    if (!isOwner && !isCollaborator && !isPublic) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to access this codespace'
      });
    }

    // Update last accessed time
    codespace.lastAccessed = Date.now();
    await codespace.save();

    res.status(200).json({
      success: true,
      codespace
    });
  } catch (error) {
    console.error('Get codespace error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Update a codespace
 * @route   PUT /api/codespaces/:id
 * @access  Private
 */
exports.updateCodespace = async (req, res) => {
  try {
    const { name, description, isPublic } = req.body;
    
    // Find codespace by ID
    let codespace = await Codespace.findById(req.params.id);

    if (!codespace) {
      return res.status(404).json({
        success: false,
        message: 'Codespace not found'
      });
    }

    // Check ownership
    if (codespace.owner.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to update this codespace'
      });
    }

    // Update fields
    if (name) codespace.name = name;
    if (description !== undefined) codespace.description = description;
    if (isPublic !== undefined) {
      codespace.isPublic = isPublic;
      
      // Generate or clear access code based on public status
      if (isPublic && !codespace.accessCode) {
        codespace.generateAccessCode();
      } else if (!isPublic) {
        codespace.accessCode = null;
      }
    }

    // Save changes
    await codespace.save();

    res.status(200).json({
      success: true,
      codespace,
      message: 'Codespace updated successfully'
    });
  } catch (error) {
    console.error('Update codespace error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Delete a codespace
 * @route   DELETE /api/codespaces/:id
 * @access  Private
 */
exports.deleteCodespace = async (req, res) => {
  try {
    const codespace = await Codespace.findById(req.params.id);

    if (!codespace) {
      return res.status(404).json({
        success: false,
        message: 'Codespace not found'
      });
    }

    // Check ownership
    if (codespace.owner.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to delete this codespace'
      });
    }

    await codespace.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Codespace deleted successfully'
    });
  } catch (error) {
    console.error('Delete codespace error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};
