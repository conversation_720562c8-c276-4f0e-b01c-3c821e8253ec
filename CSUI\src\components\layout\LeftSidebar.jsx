import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'

const LeftSidebar = ({ isCollapsed, onToggleCollapse }) => {
  const location = useLocation()
  const [recentCodespaces] = useState([
    { id: 1, name: 'React Project', language: 'javascript', lastAccessed: '2 hours ago' },
    { id: 2, name: 'Python Script', language: 'python', lastAccessed: '1 day ago' },
    { id: 3, name: 'Web App', language: 'html', lastAccessed: '3 days ago' },
  ])

  const isActive = (path) => location.pathname === path

  const navigationItems = [
    {
      path: '/dashboard',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
      label: 'Dashboard',
    },
    {
      path: '/codespaces',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      label: 'Codespaces',
    },
    {
      path: '/templates',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      label: 'Templates',
    },
    {
      path: '/shared',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
        </svg>
      ),
      label: 'Shared with me',
    },
  ]

  const getLanguageIcon = (language) => {
    switch (language) {
      case 'javascript':
        return '🟨'
      case 'python':
        return '🐍'
      case 'html':
        return '🌐'
      default:
        return '📄'
    }
  }

  return (
    <aside className={`hidden lg:flex bg-gray-800/80 backdrop-blur-sm border-r border-gray-700/50 text-white transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } flex-col`}>
      {/* Collapse toggle button */}
      <div className="p-4 border-b border-gray-700/50">
        <button
          onClick={onToggleCollapse}
          className="w-full flex items-center justify-center p-2 rounded-lg hover:bg-gray-700/50 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-5 w-5 transition-transform duration-300 ${isCollapsed ? 'rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
          </svg>
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1">
        {navigationItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={`flex items-center py-2 px-3 rounded-lg transition-colors duration-200 ${
              isActive(item.path)
                ? 'bg-green-600/20 text-green-400 border-r-2 border-green-400'
                : 'hover:bg-gray-700/70 hover:text-green-400'
            }`}
            title={isCollapsed ? item.label : ''}
          >
            <span className="text-green-400">{item.icon}</span>
            {!isCollapsed && <span className="ml-3">{item.label}</span>}
          </Link>
        ))}

        {/* Recent Codespaces Section */}
        {!isCollapsed && (
          <div className="pt-6">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Recent Codespaces
              </h3>
              <button className="text-gray-400 hover:text-green-400 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </div>

            <div className="space-y-1">
              {recentCodespaces.map((codespace) => (
                <Link
                  key={codespace.id}
                  to={`/codespace/${codespace.id}`}
                  className="block p-2 rounded-lg hover:bg-gray-700/50 transition-colors group"
                >
                  <div className="flex items-center">
                    <span className="text-lg mr-2">{getLanguageIcon(codespace.language)}</span>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-white truncate group-hover:text-green-400">
                        {codespace.name}
                      </p>
                      <p className="text-xs text-gray-400">
                        {codespace.lastAccessed}
                      </p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        {!isCollapsed && (
          <div className="pt-6">
            <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              Quick Actions
            </h3>
            <div className="space-y-1">
              <button className="w-full flex items-center py-2 px-3 rounded-lg hover:bg-gray-700/50 hover:text-green-400 transition-colors text-left">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                New Codespace
              </button>
              <button className="w-full flex items-center py-2 px-3 rounded-lg hover:bg-gray-700/50 hover:text-green-400 transition-colors text-left">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
                Import Project
              </button>
            </div>
          </div>
        )}
      </nav>

      {/* Collapsed state icons for recent codespaces */}
      {isCollapsed && (
        <div className="p-2 border-t border-gray-700/50">
          <div className="space-y-2">
            {recentCodespaces.slice(0, 3).map((codespace) => (
              <Link
                key={codespace.id}
                to={`/codespace/${codespace.id}`}
                className="block p-2 rounded-lg hover:bg-gray-700/50 transition-colors text-center"
                title={codespace.name}
              >
                <span className="text-lg">{getLanguageIcon(codespace.language)}</span>
              </Link>
            ))}
          </div>
        </div>
      )}
    </aside>
  )
}

export default LeftSidebar
