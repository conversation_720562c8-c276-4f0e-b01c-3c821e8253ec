const mongoose = require('mongoose');

const codeFileSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a file name'],
    trim: true
  },
  language: {
    type: String,
    required: [true, 'Please provide a language'],
    trim: true
  },
  content: {
    type: String,
    default: ''
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

const codespaceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a name for the codespace'],
    trim: true
  },
  description: {
    type: String,
    default: ''
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  accessCode: {
    type: String,
    default: null
  },
  files: [codeFileSchema],
  collaborators: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['viewer', 'editor', 'admin'],
      default: 'viewer'
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  lastAccessed: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field on save
codespaceSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Update the updatedAt field for files on save
codeFileSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Generate a random access code for public codespaces
codespaceSchema.methods.generateAccessCode = function() {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < 8; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  this.accessCode = result;
  return result;
};

// Create indexes for faster queries
codespaceSchema.index({ owner: 1 });
codespaceSchema.index({ 'collaborators.user': 1 });
codespaceSchema.index({ isPublic: 1 });
codespaceSchema.index({ accessCode: 1 });

const Codespace = mongoose.model('Codespace', codespaceSchema);

module.exports = Codespace;
