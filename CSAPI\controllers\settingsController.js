const UserSettings = require('../models/UserSettings');
const User = require('../models/User');

// @desc    Get user settings
// @route   GET /api/user/settings
// @access  Private
exports.getUserSettings = async (req, res) => {
  try {
    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    // If no settings exist, create default settings
    if (!userSettings) {
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: UserSettings.getDefaultEditorSettings(),
        uiSettings: UserSettings.getDefaultUISettings(),
        notificationSettings: UserSettings.getDefaultNotificationSettings(),
        generalSettings: UserSettings.getDefaultGeneralSettings()
      });

      await userSettings.save();
    }

    res.status(200).json({
      success: true,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings,
        lastUpdated: userSettings.lastUpdated
      }
    });
  } catch (error) {
    console.error('Get user settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update all user settings
// @route   PUT /api/user/settings
// @access  Private
exports.updateUserSettings = async (req, res) => {
  try {
    const { editor, ui, notifications, general } = req.body;

    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    if (!userSettings) {
      // Create new settings if they don't exist
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: editor || UserSettings.getDefaultEditorSettings(),
        uiSettings: ui || UserSettings.getDefaultUISettings(),
        notificationSettings: notifications || UserSettings.getDefaultNotificationSettings(),
        generalSettings: general || UserSettings.getDefaultGeneralSettings()
      });
    } else {
      // Update existing settings
      if (editor) {
        userSettings.editorSettings = { ...userSettings.editorSettings, ...editor };
      }
      if (ui) {
        userSettings.uiSettings = { ...userSettings.uiSettings, ...ui };
      }
      if (notifications) {
        userSettings.notificationSettings = { ...userSettings.notificationSettings, ...notifications };
      }
      if (general) {
        userSettings.generalSettings = { ...userSettings.generalSettings, ...general };
      }
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings,
        lastUpdated: userSettings.lastUpdated
      },
      message: 'Settings updated successfully'
    });
  } catch (error) {
    console.error('Update user settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update editor settings only
// @route   PATCH /api/user/settings/editor
// @access  Private
exports.updateEditorSettings = async (req, res) => {
  try {
    const editorSettings = req.body;

    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    if (!userSettings) {
      // Create new settings if they don't exist
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: { ...UserSettings.getDefaultEditorSettings(), ...editorSettings },
        uiSettings: UserSettings.getDefaultUISettings(),
        notificationSettings: UserSettings.getDefaultNotificationSettings(),
        generalSettings: UserSettings.getDefaultGeneralSettings()
      });
    } else {
      // Update only editor settings
      userSettings.editorSettings = { ...userSettings.editorSettings, ...editorSettings };
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      editorSettings: userSettings.editorSettings,
      lastUpdated: userSettings.lastUpdated,
      message: 'Editor settings updated successfully'
    });
  } catch (error) {
    console.error('Update editor settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update UI settings only
// @route   PATCH /api/user/settings/ui
// @access  Private
exports.updateUISettings = async (req, res) => {
  try {
    const uiSettings = req.body;

    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    if (!userSettings) {
      // Create new settings if they don't exist
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: UserSettings.getDefaultEditorSettings(),
        uiSettings: { ...UserSettings.getDefaultUISettings(), ...uiSettings },
        notificationSettings: UserSettings.getDefaultNotificationSettings(),
        generalSettings: UserSettings.getDefaultGeneralSettings()
      });
    } else {
      // Update only UI settings
      userSettings.uiSettings = { ...userSettings.uiSettings, ...uiSettings };
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      uiSettings: userSettings.uiSettings,
      lastUpdated: userSettings.lastUpdated,
      message: 'UI settings updated successfully'
    });
  } catch (error) {
    console.error('Update UI settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update notification settings only
// @route   PATCH /api/user/settings/notifications
// @access  Private
exports.updateNotificationSettings = async (req, res) => {
  try {
    const notificationSettings = req.body;

    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    if (!userSettings) {
      // Create new settings if they don't exist
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: UserSettings.getDefaultEditorSettings(),
        uiSettings: UserSettings.getDefaultUISettings(),
        notificationSettings: { ...UserSettings.getDefaultNotificationSettings(), ...notificationSettings },
        generalSettings: UserSettings.getDefaultGeneralSettings()
      });
    } else {
      // Update only notification settings
      userSettings.notificationSettings = { ...userSettings.notificationSettings, ...notificationSettings };
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      notificationSettings: userSettings.notificationSettings,
      lastUpdated: userSettings.lastUpdated,
      message: 'Notification settings updated successfully'
    });
  } catch (error) {
    console.error('Update notification settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update general settings only
// @route   PATCH /api/user/settings/general
// @access  Private
exports.updateGeneralSettings = async (req, res) => {
  try {
    const generalSettings = req.body;

    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    if (!userSettings) {
      // Create new settings if they don't exist
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: UserSettings.getDefaultEditorSettings(),
        uiSettings: UserSettings.getDefaultUISettings(),
        notificationSettings: UserSettings.getDefaultNotificationSettings(),
        generalSettings: { ...UserSettings.getDefaultGeneralSettings(), ...generalSettings }
      });
    } else {
      // Update only general settings
      userSettings.generalSettings = { ...userSettings.generalSettings, ...generalSettings };
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      generalSettings: userSettings.generalSettings,
      lastUpdated: userSettings.lastUpdated,
      message: 'General settings updated successfully'
    });
  } catch (error) {
    console.error('Update general settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Reset user settings to defaults
// @route   POST /api/user/settings/reset
// @access  Private
exports.resetUserSettings = async (req, res) => {
  try {
    const { category } = req.body; // Optional: reset specific category

    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    if (!userSettings) {
      // Create new settings with defaults
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: UserSettings.getDefaultEditorSettings(),
        uiSettings: UserSettings.getDefaultUISettings(),
        notificationSettings: UserSettings.getDefaultNotificationSettings(),
        generalSettings: UserSettings.getDefaultGeneralSettings()
      });
    } else {
      // Reset specific category or all settings
      if (category) {
        switch (category) {
          case 'editor':
            userSettings.editorSettings = UserSettings.getDefaultEditorSettings();
            break;
          case 'ui':
            userSettings.uiSettings = UserSettings.getDefaultUISettings();
            break;
          case 'notifications':
            userSettings.notificationSettings = UserSettings.getDefaultNotificationSettings();
            break;
          case 'general':
            userSettings.generalSettings = UserSettings.getDefaultGeneralSettings();
            break;
          default:
            return res.status(400).json({
              success: false,
              message: 'Invalid category. Use: editor, ui, notifications, or general'
            });
        }
      } else {
        // Reset all settings
        userSettings.editorSettings = UserSettings.getDefaultEditorSettings();
        userSettings.uiSettings = UserSettings.getDefaultUISettings();
        userSettings.notificationSettings = UserSettings.getDefaultNotificationSettings();
        userSettings.generalSettings = UserSettings.getDefaultGeneralSettings();
      }
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings,
        lastUpdated: userSettings.lastUpdated
      },
      message: category ? `${category} settings reset to defaults` : 'All settings reset to defaults'
    });
  } catch (error) {
    console.error('Reset user settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get settings schema/defaults
// @route   GET /api/user/settings/schema
// @access  Private
exports.getSettingsSchema = async (req, res) => {
  try {
    const schema = {
      editor: UserSettings.getDefaultEditorSettings(),
      ui: UserSettings.getDefaultUISettings(),
      notifications: UserSettings.getDefaultNotificationSettings(),
      general: UserSettings.getDefaultGeneralSettings()
    };

    res.status(200).json({
      success: true,
      schema,
      message: 'Settings schema retrieved successfully'
    });
  } catch (error) {
    console.error('Get settings schema error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get formatted settings for frontend
// @route   GET /api/user/settings/formatted
// @access  Private
exports.getFormattedSettings = async (req, res) => {
  try {
    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    // If no settings exist, return defaults
    if (!userSettings) {
      const defaults = UserSettings.getDefaults();
      return res.status(200).json({
        success: true,
        settings: defaults,
        isDefault: true,
        message: 'Default settings returned (user has no saved settings)'
      });
    }

    // Format settings for frontend consumption
    const formattedSettings = {
      ui: userSettings.uiSettings,
      editor: userSettings.editorSettings,
      notifications: userSettings.notificationSettings,
      general: userSettings.generalSettings,
      lastUpdated: userSettings.lastUpdated
    };

    res.status(200).json({
      success: true,
      settings: formattedSettings,
      isDefault: false,
      message: 'User settings retrieved successfully'
    });
  } catch (error) {
    console.error('Get formatted settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Bulk update settings (for syncing from frontend)
// @route   POST /api/user/settings/sync
// @access  Private
exports.syncSettings = async (req, res) => {
  try {
    const { ui, editor, notifications, general } = req.body;

    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    if (!userSettings) {
      // Create new settings
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: editor || UserSettings.getDefaultEditorSettings(),
        uiSettings: ui || UserSettings.getDefaultUISettings(),
        notificationSettings: notifications || UserSettings.getDefaultNotificationSettings(),
        generalSettings: general || UserSettings.getDefaultGeneralSettings()
      });
    } else {
      // Merge with existing settings
      if (ui) userSettings.uiSettings = { ...userSettings.uiSettings, ...ui };
      if (editor) userSettings.editorSettings = { ...userSettings.editorSettings, ...editor };
      if (notifications) userSettings.notificationSettings = { ...userSettings.notificationSettings, ...notifications };
      if (general) userSettings.generalSettings = { ...userSettings.generalSettings, ...general };
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      settings: {
        ui: userSettings.uiSettings,
        editor: userSettings.editorSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings,
        lastUpdated: userSettings.lastUpdated
      },
      message: 'Settings synchronized successfully'
    });
  } catch (error) {
    console.error('Sync settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Export user settings
// @route   GET /api/user/settings/export
// @access  Private
exports.exportSettings = async (req, res) => {
  try {
    const userSettings = await UserSettings.findOne({ userId: req.user.id });

    if (!userSettings) {
      return res.status(404).json({
        success: false,
        message: 'No settings found to export'
      });
    }

    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      userId: req.user.id,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings
      }
    };

    res.status(200).json({
      success: true,
      exportData,
      message: 'Settings exported successfully'
    });
  } catch (error) {
    console.error('Export settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Import user settings
// @route   POST /api/user/settings/import
// @access  Private
exports.importSettings = async (req, res) => {
  try {
    const { settings, overwrite = false } = req.body;

    if (!settings) {
      return res.status(400).json({
        success: false,
        message: 'Settings data is required'
      });
    }

    let userSettings = await UserSettings.findOne({ userId: req.user.id });

    if (!userSettings) {
      // Create new settings
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: settings.editor || UserSettings.getDefaultEditorSettings(),
        uiSettings: settings.ui || UserSettings.getDefaultUISettings(),
        notificationSettings: settings.notifications || UserSettings.getDefaultNotificationSettings(),
        generalSettings: settings.general || UserSettings.getDefaultGeneralSettings()
      });
    } else {
      if (overwrite) {
        // Completely replace settings
        userSettings.editorSettings = settings.editor || UserSettings.getDefaultEditorSettings();
        userSettings.uiSettings = settings.ui || UserSettings.getDefaultUISettings();
        userSettings.notificationSettings = settings.notifications || UserSettings.getDefaultNotificationSettings();
        userSettings.generalSettings = settings.general || UserSettings.getDefaultGeneralSettings();
      } else {
        // Merge with existing settings
        if (settings.editor) userSettings.editorSettings = { ...userSettings.editorSettings, ...settings.editor };
        if (settings.ui) userSettings.uiSettings = { ...userSettings.uiSettings, ...settings.ui };
        if (settings.notifications) userSettings.notificationSettings = { ...userSettings.notificationSettings, ...settings.notifications };
        if (settings.general) userSettings.generalSettings = { ...userSettings.generalSettings, ...settings.general };
      }
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings,
        lastUpdated: userSettings.lastUpdated
      },
      message: 'Settings imported successfully'
    });
  } catch (error) {
    console.error('Import settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};
