const UserSettings = require('../models/UserSettings');
const User = require('../models/User');

// @desc    Get user settings
// @route   GET /api/user/settings
// @access  Private
exports.getUserSettings = async (req, res) => {
  try {
    let userSettings = await UserSettings.findOne({ userId: req.user.id });
    
    // If no settings exist, create default settings
    if (!userSettings) {
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: UserSettings.getDefaultEditorSettings(),
        uiSettings: UserSettings.getDefaultUISettings(),
        notificationSettings: UserSettings.getDefaultNotificationSettings(),
        generalSettings: UserSettings.getDefaultGeneralSettings()
      });
      
      await userSettings.save();
    }

    res.status(200).json({
      success: true,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings,
        lastUpdated: userSettings.updatedAt
      }
    });
  } catch (error) {
    console.error('Get user settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update all user settings
// @route   PUT /api/user/settings
// @access  Private
exports.updateUserSettings = async (req, res) => {
  try {
    const { editor, ui, notifications, general } = req.body;

    let userSettings = await UserSettings.findOne({ userId: req.user.id });
    
    if (!userSettings) {
      // Create new settings if they don't exist
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: editor || UserSettings.getDefaultEditorSettings(),
        uiSettings: ui || UserSettings.getDefaultUISettings(),
        notificationSettings: notifications || UserSettings.getDefaultNotificationSettings(),
        generalSettings: general || UserSettings.getDefaultGeneralSettings()
      });
    } else {
      // Update existing settings
      if (editor) userSettings.editorSettings = { ...userSettings.editorSettings, ...editor };
      if (ui) userSettings.uiSettings = { ...userSettings.uiSettings, ...ui };
      if (notifications) userSettings.notificationSettings = { ...userSettings.notificationSettings, ...notifications };
      if (general) userSettings.generalSettings = { ...userSettings.generalSettings, ...general };
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings,
        lastUpdated: userSettings.updatedAt
      },
      message: 'Settings updated successfully'
    });
  } catch (error) {
    console.error('Update user settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update specific settings category
// @route   PATCH /api/user/settings/:category
// @access  Private
exports.updateSettingsCategory = async (req, res) => {
  try {
    const { category } = req.params;
    const settingsData = req.body;

    // Validate category
    const validCategories = ['editor', 'ui', 'notifications', 'general'];
    if (!validCategories.includes(category)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid category. Use: editor, ui, notifications, or general'
      });
    }

    let userSettings = await UserSettings.findOne({ userId: req.user.id });
    
    if (!userSettings) {
      // Create new settings if they don't exist
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: UserSettings.getDefaultEditorSettings(),
        uiSettings: UserSettings.getDefaultUISettings(),
        notificationSettings: UserSettings.getDefaultNotificationSettings(),
        generalSettings: UserSettings.getDefaultGeneralSettings()
      });
    }

    // Update specific category
    const categoryMap = {
      editor: 'editorSettings',
      ui: 'uiSettings',
      notifications: 'notificationSettings',
      general: 'generalSettings'
    };

    const settingsField = categoryMap[category];
    userSettings[settingsField] = { ...userSettings[settingsField], ...settingsData };

    await userSettings.save();

    res.status(200).json({
      success: true,
      [category]: userSettings[settingsField],
      lastUpdated: userSettings.updatedAt,
      message: `${category} settings updated successfully`
    });
  } catch (error) {
    console.error('Update settings category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Reset user settings to defaults
// @route   POST /api/user/settings/reset
// @access  Private
exports.resetUserSettings = async (req, res) => {
  try {
    const { category } = req.body; // Optional: reset specific category

    let userSettings = await UserSettings.findOne({ userId: req.user.id });
    
    if (!userSettings) {
      // Create new settings with defaults
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: UserSettings.getDefaultEditorSettings(),
        uiSettings: UserSettings.getDefaultUISettings(),
        notificationSettings: UserSettings.getDefaultNotificationSettings(),
        generalSettings: UserSettings.getDefaultGeneralSettings()
      });
    } else {
      // Reset specific category or all settings
      if (category) {
        userSettings.resetCategory(category);
      } else {
        userSettings.resetAllSettings();
      }
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings,
        lastUpdated: userSettings.updatedAt
      },
      message: category ? `${category} settings reset to defaults` : 'All settings reset to defaults'
    });
  } catch (error) {
    console.error('Reset user settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get settings schema/defaults
// @route   GET /api/user/settings/schema
// @access  Private
exports.getSettingsSchema = async (req, res) => {
  try {
    const schema = UserSettings.getDefaults();

    res.status(200).json({
      success: true,
      schema,
      message: 'Settings schema retrieved successfully'
    });
  } catch (error) {
    console.error('Get settings schema error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Export user settings
// @route   GET /api/user/settings/export
// @access  Private
exports.exportUserSettings = async (req, res) => {
  try {
    const userSettings = await UserSettings.findOne({ userId: req.user.id });
    
    if (!userSettings) {
      return res.status(404).json({
        success: false,
        message: 'No settings found for user'
      });
    }

    const exportData = {
      exportedAt: new Date().toISOString(),
      userId: req.user.id,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings
      }
    };

    res.status(200).json({
      success: true,
      data: exportData,
      message: 'Settings exported successfully'
    });
  } catch (error) {
    console.error('Export settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Import user settings
// @route   POST /api/user/settings/import
// @access  Private
exports.importUserSettings = async (req, res) => {
  try {
    const { settings, overwrite = false } = req.body;

    if (!settings) {
      return res.status(400).json({
        success: false,
        message: 'Settings data is required'
      });
    }

    let userSettings = await UserSettings.findOne({ userId: req.user.id });
    
    if (!userSettings) {
      // Create new settings
      userSettings = new UserSettings({
        userId: req.user.id,
        editorSettings: settings.editor || UserSettings.getDefaultEditorSettings(),
        uiSettings: settings.ui || UserSettings.getDefaultUISettings(),
        notificationSettings: settings.notifications || UserSettings.getDefaultNotificationSettings(),
        generalSettings: settings.general || UserSettings.getDefaultGeneralSettings()
      });
    } else {
      // Update existing settings
      if (overwrite) {
        // Completely replace settings
        userSettings.editorSettings = settings.editor || UserSettings.getDefaultEditorSettings();
        userSettings.uiSettings = settings.ui || UserSettings.getDefaultUISettings();
        userSettings.notificationSettings = settings.notifications || UserSettings.getDefaultNotificationSettings();
        userSettings.generalSettings = settings.general || UserSettings.getDefaultGeneralSettings();
      } else {
        // Merge with existing settings
        if (settings.editor) userSettings.editorSettings = { ...userSettings.editorSettings, ...settings.editor };
        if (settings.ui) userSettings.uiSettings = { ...userSettings.uiSettings, ...settings.ui };
        if (settings.notifications) userSettings.notificationSettings = { ...userSettings.notificationSettings, ...settings.notifications };
        if (settings.general) userSettings.generalSettings = { ...userSettings.generalSettings, ...settings.general };
      }
    }

    await userSettings.save();

    res.status(200).json({
      success: true,
      settings: {
        editor: userSettings.editorSettings,
        ui: userSettings.uiSettings,
        notifications: userSettings.notificationSettings,
        general: userSettings.generalSettings,
        lastUpdated: userSettings.updatedAt
      },
      message: 'Settings imported successfully'
    });
  } catch (error) {
    console.error('Import settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};
