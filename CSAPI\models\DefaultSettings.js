const mongoose = require('mongoose');

// Default Settings Schema - These are the system-wide defaults
const defaultSettingsSchema = new mongoose.Schema({
  category: {
    type: String,
    required: true,
    enum: ['editor', 'ui', 'notifications', 'general'],
    unique: true
  },
  
  settings: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  
  version: {
    type: String,
    default: '1.0.0'
  },
  
  description: {
    type: String,
    required: true
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update updatedAt on save
defaultSettingsSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static method to get all default settings
defaultSettingsSchema.statics.getAllDefaults = async function() {
  try {
    const defaults = await this.find({ isActive: true });
    const result = {};
    
    defaults.forEach(defaultSetting => {
      result[defaultSetting.category] = defaultSetting.settings;
    });
    
    return result;
  } catch (error) {
    console.error('Error getting default settings:', error);
    return {};
  }
};

// Static method to get default settings for a specific category
defaultSettingsSchema.statics.getDefaultsForCategory = async function(category) {
  try {
    const defaultSetting = await this.findOne({ category, isActive: true });
    return defaultSetting ? defaultSetting.settings : {};
  } catch (error) {
    console.error(`Error getting default settings for ${category}:`, error);
    return {};
  }
};

// Static method to initialize default settings
defaultSettingsSchema.statics.initializeDefaults = async function() {
  try {
    // Check if defaults already exist
    const existingDefaults = await this.countDocuments();
    if (existingDefaults > 0) {
      console.log('Default settings already exist in database');
      return;
    }

    // Editor defaults
    const editorDefaults = {
      // Basic Settings
      fontSize: 14,
      fontFamily: "'Fira Code', 'Cascadia Code', 'JetBrains Mono', 'SF Mono', Monaco, Menlo, 'Ubuntu Mono', monospace",
      fontWeight: 'normal',
      lineHeight: 1.5,
      letterSpacing: 0,
      
      // Theme and Appearance
      theme: 'vs-dark',
      colorTheme: 'codespace-dark',
      
      // Line Numbers
      lineNumbers: 'on',
      lineNumbersMinChars: 3,
      
      // Indentation
      tabSize: 2,
      insertSpaces: true,
      detectIndentation: true,
      trimAutoWhitespace: true,
      
      // Word Wrapping
      wordWrap: 'on',
      wordWrapColumn: 80,
      wordWrapMinified: true,
      wrappingIndent: 'indent',
      
      // Scrolling
      scrollBeyondLastLine: false,
      scrollBeyondLastColumn: 5,
      smoothScrolling: true,
      mouseWheelScrollSensitivity: 1,
      fastScrollSensitivity: 5,
      
      // Cursor
      cursorStyle: 'line',
      cursorWidth: 2,
      cursorBlinking: 'smooth',
      cursorSmoothCaretAnimation: true,
      
      // Selection
      selectOnLineNumbers: true,
      selectionHighlight: true,
      occurrencesHighlight: true,
      renderLineHighlight: 'all',
      renderLineHighlightOnlyWhenFocus: false,
      
      // Minimap
      minimapEnabled: true,
      minimapSide: 'right',
      minimapSize: 'proportional',
      minimapShowSlider: 'mouseover',
      minimapRenderCharacters: true,
      minimapMaxColumn: 120,
      
      // Code Folding
      folding: true,
      foldingStrategy: 'auto',
      foldingHighlight: true,
      unfoldOnClickAfterEndOfLine: false,
      
      // Bracket Matching
      matchBrackets: 'always',
      bracketPairColorization: true,
      
      // Guides
      guides: {
        bracketPairs: true,
        bracketPairsHorizontal: true,
        highlightActiveBracketPair: true,
        indentation: true,
        highlightActiveIndentation: true
      },
      
      // IntelliSense
      quickSuggestions: true,
      quickSuggestionsDelay: 10,
      suggestOnTriggerCharacters: true,
      acceptSuggestionOnEnter: 'on',
      acceptSuggestionOnCommitCharacter: true,
      snippetSuggestions: 'top',
      emptySelectionClipboard: true,
      wordBasedSuggestions: true,
      suggestSelection: 'recentlyUsed',
      suggestFontSize: 0,
      suggestLineHeight: 0,
      
      // Auto-completion and Formatting
      autoIndent: 'full',
      formatOnType: true,
      formatOnPaste: true,
      autoClosingBrackets: 'always',
      autoClosingQuotes: 'always',
      autoSurround: 'languageDefined',
      
      // Find and Replace
      find: {
        seedSearchStringFromSelection: 'always',
        autoFindInSelection: 'never',
        addExtraSpaceOnTop: true,
        loop: true
      },
      
      // Whitespace and Rendering
      renderWhitespace: 'selection',
      renderControlCharacters: false,
      renderIndentGuides: true,
      highlightActiveIndentGuide: true,
      renderValidationDecorations: 'editable',
      
      // Performance
      stopRenderingLineAfter: 10000,
      disableLayerHinting: false,
      disableMonospaceOptimizations: false,
      
      // Accessibility
      accessibilitySupport: 'auto',
      accessibilityPageSize: 10,
      
      // Advanced
      contextmenu: true,
      mouseStyle: 'text',
      multiCursorModifier: 'alt',
      multiCursorMergeOverlapping: true,
      multiCursorPaste: 'spread',
      columnSelection: false,
      copyWithSyntaxHighlighting: true,
      useTabStops: true,
      
      // Editor Layout
      automaticLayout: true,
      glyphMargin: true,
      lineDecorationsWidth: 10,
      lineNumbersWidth: 50,
      overviewRulerBorder: true,
      overviewRulerLanes: 3,
      hideCursorInOverviewRuler: false,
      
      // Hover
      hover: {
        enabled: true,
        delay: 300,
        sticky: true
      },
      
      // Parameter Hints
      parameterHints: {
        enabled: true,
        cycle: false
      },
      
      // Code Lens
      codeLens: true,
      codeLensFontFamily: '',
      codeLensFontSize: 12,
      
      // Links
      links: true,
      
      // Comments
      comments: {
        insertSpace: true,
        ignoreEmptyLines: true
      }
    };

    // UI defaults
    const uiDefaults = {
      showFooter: false,
      allowMultiplePanels: true,
      primaryPanelToggleBehavior: 'shrink',
      secondaryPanelToggleBehavior: 'shrink',
      isPrimaryPanelCollapsed: false,
      isSecondaryPanelCollapsed: false
    };

    // Notification defaults
    const notificationDefaults = {
      email: true,
      push: false,
      desktop: true,
      sound: true,
      codespaceInvites: true,
      collaborationUpdates: true,
      systemUpdates: true
    };

    // General defaults
    const generalDefaults = {
      language: 'en',
      timezone: 'auto',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      autoSave: true,
      autoSaveInterval: 30
    };

    // Create default settings documents
    const defaultSettings = [
      {
        category: 'editor',
        settings: editorDefaults,
        description: 'Default Monaco Editor settings for all users'
      },
      {
        category: 'ui',
        settings: uiDefaults,
        description: 'Default UI layout and panel settings'
      },
      {
        category: 'notifications',
        settings: notificationDefaults,
        description: 'Default notification preferences'
      },
      {
        category: 'general',
        settings: generalDefaults,
        description: 'Default general application settings'
      }
    ];

    await this.insertMany(defaultSettings);
    console.log('✅ Default settings initialized successfully');
    
  } catch (error) {
    console.error('Error initializing default settings:', error);
  }
};

module.exports = mongoose.model('DefaultSettings', defaultSettingsSchema);
