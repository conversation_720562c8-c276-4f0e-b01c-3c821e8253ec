import { create } from 'zustand'
import { persist } from 'zustand/middleware'

const useUISettingsStore = create(
  persist(
    (set, get) => ({
      // UI Layout Settings
      showFooter: false, // Footer is hidden by default
      primaryPanelMode: 'visible', // 'hidden', 'visible'
      secondaryPanelMode: 'hidden', // 'hidden', 'visible'
      allowMultiplePanels: true, // Allow both panels to be open simultaneously

      // Panel Toggle Behavior
      primaryPanelToggleBehavior: 'shrink', // 'shrink' or 'hide'
      secondaryPanelToggleBehavior: 'shrink', // 'shrink' or 'hide'

      // Panel States
      isPrimaryPanelCollapsed: false,
      isSecondaryPanelCollapsed: false,

      // Actions
      toggleFooter: () => set((state) => ({ showFooter: !state.showFooter })),

      setPrimaryPanelMode: (mode) => set({ primaryPanelMode: mode }),
      setSecondaryPanelMode: (mode) => set({ secondaryPanelMode: mode }),

      setPrimaryPanelToggleBehavior: (behavior) => set({ primaryPanelToggleBehavior: behavior }),
      setSecondaryPanelToggleBehavior: (behavior) => set({ secondaryPanelToggleBehavior: behavior }),

      togglePrimaryPanel: () => set((state) => {
        const currentState = get();

        if (currentState.primaryPanelToggleBehavior === 'hide') {
          // Hide/Show behavior - toggle visibility completely
          const newMode = state.primaryPanelMode === 'visible' ? 'hidden' : 'visible';

          // If only one panel allowed and we're showing primary, hide secondary
          if (!currentState.allowMultiplePanels && newMode === 'visible') {
            return {
              primaryPanelMode: newMode,
              secondaryPanelMode: 'hidden'
            };
          }

          return { primaryPanelMode: newMode };
        } else {
          // Shrink/Expand behavior - toggle collapsed state
          const newCollapsed = !state.isPrimaryPanelCollapsed;

          // If only one panel allowed and we're expanding primary, collapse secondary
          if (!currentState.allowMultiplePanels && !newCollapsed) {
            return {
              isPrimaryPanelCollapsed: newCollapsed,
              isSecondaryPanelCollapsed: true
            };
          }

          return { isPrimaryPanelCollapsed: newCollapsed };
        }
      }),

      toggleSecondaryPanel: () => set((state) => {
        const currentState = get();

        if (currentState.secondaryPanelToggleBehavior === 'hide') {
          // Hide/Show behavior - toggle visibility completely
          const newMode = state.secondaryPanelMode === 'visible' ? 'hidden' : 'visible';

          // If only one panel allowed and we're showing secondary, hide primary
          if (!currentState.allowMultiplePanels && newMode === 'visible') {
            return {
              secondaryPanelMode: newMode,
              primaryPanelMode: 'hidden'
            };
          }

          return { secondaryPanelMode: newMode };
        } else {
          // Shrink/Expand behavior - toggle collapsed state
          const newCollapsed = !state.isSecondaryPanelCollapsed;

          // If only one panel allowed and we're expanding secondary, collapse primary
          if (!currentState.allowMultiplePanels && !newCollapsed) {
            return {
              isSecondaryPanelCollapsed: newCollapsed,
              isPrimaryPanelCollapsed: true
            };
          }

          return { isSecondaryPanelCollapsed: newCollapsed };
        }
      }),

      setAllowMultiplePanels: (allow) => set({ allowMultiplePanels: allow }),

      // Reset to defaults
      resetUISettings: () => set({
        showFooter: false,
        primaryPanelMode: 'visible',
        secondaryPanelMode: 'hidden',
        allowMultiplePanels: true,
        primaryPanelToggleBehavior: 'shrink',
        secondaryPanelToggleBehavior: 'shrink',
        isPrimaryPanelCollapsed: false,
        isSecondaryPanelCollapsed: false,
      }),

      // Get panel visibility based on mode
      isPrimaryPanelVisible: () => {
        const state = get();
        return state.primaryPanelMode === 'visible';
      },

      isSecondaryPanelVisible: () => {
        const state = get();
        return state.secondaryPanelMode === 'visible';
      },
    }),
    {
      name: 'ui-settings-storage',
      partialize: (state) => ({
        showFooter: state.showFooter,
        primaryPanelMode: state.primaryPanelMode,
        secondaryPanelMode: state.secondaryPanelMode,
        allowMultiplePanels: state.allowMultiplePanels,
        primaryPanelToggleBehavior: state.primaryPanelToggleBehavior,
        secondaryPanelToggleBehavior: state.secondaryPanelToggleBehavior,
        isPrimaryPanelCollapsed: state.isPrimaryPanelCollapsed,
        isSecondaryPanelCollapsed: state.isSecondaryPanelCollapsed,
      }),
    }
  )
)

export default useUISettingsStore
