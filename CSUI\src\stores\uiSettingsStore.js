import { create } from 'zustand'
import { persist } from 'zustand/middleware'

const useUISettingsStore = create(
  persist(
    (set, get) => ({
      // UI Layout Settings
      showFooter: false, // Footer is hidden by default
      primaryPanelMode: 'collapsed', // 'hidden', 'collapsed', 'expanded'
      secondaryPanelMode: 'hidden', // 'hidden', 'collapsed', 'expanded'
      allowMultiplePanels: true, // Allow both panels to be open simultaneously
      
      // Panel States
      isPrimaryPanelCollapsed: false,
      isSecondaryPanelCollapsed: false,
      
      // Actions
      toggleFooter: () => set((state) => ({ showFooter: !state.showFooter })),
      
      setPrimaryPanelMode: (mode) => set({ primaryPanelMode: mode }),
      setSecondaryPanelMode: (mode) => set({ secondaryPanelMode: mode }),
      
      togglePrimaryPanel: () => set((state) => {
        const newCollapsed = !state.isPrimaryPanelCollapsed;
        const currentState = get();
        
        // If only one panel allowed and we're opening primary, close secondary
        if (!currentState.allowMultiplePanels && !newCollapsed) {
          return {
            isPrimaryPanelCollapsed: newCollapsed,
            isSecondaryPanelCollapsed: true
          };
        }
        
        return { isPrimaryPanelCollapsed: newCollapsed };
      }),
      
      toggleSecondaryPanel: () => set((state) => {
        const newCollapsed = !state.isSecondaryPanelCollapsed;
        const currentState = get();
        
        // If only one panel allowed and we're opening secondary, close primary
        if (!currentState.allowMultiplePanels && !newCollapsed) {
          return {
            isSecondaryPanelCollapsed: newCollapsed,
            isPrimaryPanelCollapsed: true
          };
        }
        
        return { isSecondaryPanelCollapsed: newCollapsed };
      }),
      
      setAllowMultiplePanels: (allow) => set({ allowMultiplePanels: allow }),
      
      // Reset to defaults
      resetUISettings: () => set({
        showFooter: false,
        primaryPanelMode: 'collapsed',
        secondaryPanelMode: 'hidden',
        allowMultiplePanels: true,
        isPrimaryPanelCollapsed: false,
        isSecondaryPanelCollapsed: false,
      }),
      
      // Get panel visibility based on mode
      isPrimaryPanelVisible: () => {
        const state = get();
        return state.primaryPanelMode !== 'hidden';
      },
      
      isSecondaryPanelVisible: () => {
        const state = get();
        return state.secondaryPanelMode !== 'hidden';
      },
    }),
    {
      name: 'ui-settings-storage',
      partialize: (state) => ({
        showFooter: state.showFooter,
        primaryPanelMode: state.primaryPanelMode,
        secondaryPanelMode: state.secondaryPanelMode,
        allowMultiplePanels: state.allowMultiplePanels,
        isPrimaryPanelCollapsed: state.isPrimaryPanelCollapsed,
        isSecondaryPanelCollapsed: state.isSecondaryPanelCollapsed,
      }),
    }
  )
)

export default useUISettingsStore
