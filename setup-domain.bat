@echo off
echo Setting up custom domain for CodeSpace...
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with Administrator privileges...
) else (
    echo This script requires Administrator privileges.
    echo Please right-click and "Run as administrator"
    pause
    exit /b 1
)

REM Add domain to hosts file
echo 127.0.0.1    codespace.roy >> C:\Windows\System32\drivers\etc\hosts

echo.
echo Custom domain 'codespace.roy' has been added to your hosts file.
echo.
echo Next steps:
echo 1. Start API server: cd CSAPI ^&^& npm run dev
echo 2. Start UI server: cd CSUI ^&^& npm run dev  
echo 3. Access at: http://codespace.roy:5173
echo.
pause
